"""
OF (Ordre de Fabrication) routes - Based on your constructor example.
This module provides all possible routes for OF data that can be used 
inside the app or from external projects.
"""

from fastapi import APIRouter, HTTPException, Query, Depends
from typing import Optional, List, Dict, Any
from datetime import datetime
from app.core.database import get_db_connection, get_analyzer
from app.models.of_models import OFResponse, OFFilters
from app.models.common import SuccessResponse

router = APIRouter(prefix="/api/of", tags=["OF - Ordres de Fabrication"])


@router.get("/en_cours", response_model=SuccessResponse)
def get_of_en_cours():
    """
    Get OF en cours - matches your constructor example /en_cours endpoint.
    Returns all production orders currently in progress.
    """
    conn = get_db_connection()
    cursor = conn.cursor()
    query = """
    SELECT 
        OF_DA.NUMERO_OFDA,
        OF_DA.PRODUIT,
        OF_DA.STATUT,
        OF_DA.LANCEMENT_AU_PLUS_TARD,
        OF_DA.QUANTITE_DEMANDEE,
        OF_DA.CUMUL_ENTREES,
        OF_DA.DUREE_PREVUE,
        OF_DA.CUMUL_TEMPS_PASSES,
        OF_DA.AFFAIRE,
        OF_DA.DESIGNATION,
        OF_DA.LANCE_LE
    FROM gpao.OF_DA OF_DA
    WHERE OF_DA.NUMERO_OFDA LIKE 'F%' AND OF_DA.STATUT LIKE 'C%'
    """
    cursor.execute(query)
    rows = cursor.fetchall()
    of_list = []

    for row in rows:
        # Parse LANCEMENT_AU_PLUS_TARD for Semaine calculation
        lancement_au_plus_tard = row[3]
        if isinstance(lancement_au_plus_tard, datetime):
            semaine = lancement_au_plus_tard.isocalendar()[1]  # ISO week number
        else:
            semaine = None

        # Calculating Avancement_Prod safely
        quantite_demandee = row[4]
        cumul_entrees = row[5]
        if quantite_demandee and quantite_demandee != 0:
            avancement_prod = cumul_entrees / quantite_demandee
        else:
            avancement_prod = None

        # Calculating Avancement_Temps safely
        duree_prevue = row[6]
        cumul_temps_passes = row[7]
        if duree_prevue and duree_prevue != 0:
            avancement_temps = cumul_temps_passes / duree_prevue
        else:
            avancement_temps = None

        of_list.append({
            "NUMERO_OFDA": row[0],
            "PRODUIT": row[1],
            "STATUT": row[2],
            "LANCEMENT_AU_PLUS_TARD": row[3],
            "QUANTITE_DEMANDEE": row[4],
            "CUMUL_ENTREES": row[5],
            "DUREE_PREVUE": row[6],
            "CUMUL_TEMPS_PASSES": row[7],
            "AFFAIRE": row[8],
            "DESIGNATION": row[9],
            "LANCE_LE": row[10],
            "Semaine": semaine,
            "Avancement_Prod": avancement_prod,
            "Avancement_Temps": avancement_temps,
        })

    cursor.close()
    conn.close()
    return SuccessResponse(data={"of_list": of_list, "count": len(of_list)})


@router.get("/histo", response_model=SuccessResponse)
def get_of_histo():
    """
    Get OF historique - matches your constructor example /histo endpoint.
    Returns historical production orders data.
    """
    conn = get_db_connection()
    cursor = conn.cursor()
    query = """
    SELECT PRODUIT,
           CATEGORIE,
           DUREE_PREVUE,
           CUMUL_TEMPS_PASSES,
           CUMUL_ENTREES,
           LANCEMENT_AU_PLUS_TARD,
           AFFAIRE,
           NUMERO_OFDA,
           STATUT,
           LANCE_LE,
           DESIGNATION,
           QUANTITE_DEMANDEE
    FROM gpao.HISTO_OF_DA
    ORDER BY PRODUIT
    """
    cursor.execute(query)
    rows = cursor.fetchall()
    of_list = []

    for row in rows:
        lancement_au_plus_tard = row[5]
        semaine = None
        if isinstance(lancement_au_plus_tard, datetime):
            semaine = lancement_au_plus_tard.isocalendar()[1]

        quantite_demandee = row[11]
        cumul_entrees = row[4]
        cumul_temps_passes = row[3]
        duree_prevue = row[2]

        avancement_prod = None
        if quantite_demandee and quantite_demandee != 0:
            avancement_prod = cumul_entrees / quantite_demandee

        avancement_temps = None
        if duree_prevue and duree_prevue != 0:
            avancement_temps = cumul_temps_passes / duree_prevue

        of_list.append({
            "PRODUIT": row[0],
            "CATEGORIE": row[1],
            "DUREE_PREVUE": duree_prevue,
            "CUMUL_TEMPS_PASSES": cumul_temps_passes,
            "CUMUL_ENTREES": cumul_entrees,
            "LANCEMENT_AU_PLUS_TARD": lancement_au_plus_tard,
            "AFFAIRE": row[6],
            "NUMERO_OFDA": row[7],
            "STATUT": row[8],
            "LANCE_LE": row[9],
            "DESIGNATION": row[10],
            "QUANTITE_DEMANDEE": quantite_demandee,
            "Semaine": semaine,
            "Avancement_Prod": avancement_prod,
            "Avancement_Temps": avancement_temps,
        })

    cursor.close()
    conn.close()
    return SuccessResponse(data={"of_list": of_list, "count": len(of_list)})


@router.get("/all", response_model=SuccessResponse)
def get_all_common_data():
    """
    Get all OF data (combined en_cours and histo) - matches your constructor example /all endpoint.
    Returns combined data from both current and historical production orders.
    """
    conn = get_db_connection()
    cursor = conn.cursor()

    # Query for OF_DA (en_cours)
    query_en_cours = """
    SELECT PRODUIT,
           NULL AS CATEGORIE,
           DUREE_PREVUE,
           CUMUL_TEMPS_PASSES,
           CUMUL_ENTREES,
           LANCEMENT_AU_PLUS_TARD,
           AFFAIRE,
           NUMERO_OFDA,
           STATUT,
           LANCE_LE,
           DESIGNATION,
           QUANTITE_DEMANDEE
    FROM gpao.OF_DA
    WHERE NUMERO_OFDA LIKE 'F%' AND STATUT LIKE 'C%'
    """

    # Query for HISTO_OF_DA (historical)
    query_histo = """
    SELECT PRODUIT,
           CATEGORIE,
           DUREE_PREVUE,
           CUMUL_TEMPS_PASSES,
           CUMUL_ENTREES,
           LANCEMENT_AU_PLUS_TARD,
           AFFAIRE,
           NUMERO_OFDA,
           STATUT,
           LANCE_LE,
           DESIGNATION,
           QUANTITE_DEMANDEE
    FROM gpao.HISTO_OF_DA
    """

    cursor.execute(query_en_cours)
    rows_en_cours = cursor.fetchall()

    cursor.execute(query_histo)
    rows_histo = cursor.fetchall()

    combined_rows = rows_en_cours + rows_histo

    result_list = []

    for row in combined_rows:
        lancement_au_plus_tard = row[5]
        semaine = None
        if isinstance(lancement_au_plus_tard, datetime):
            semaine = lancement_au_plus_tard.isocalendar()[1]  # WEEKNUM equivalent in Python

        quantite_demandee = row[11]
        cumul_entrees = row[4]
        cumul_temps_passes = row[3]
        duree_prevue = row[2]

        avancement_prod = None
        if quantite_demandee and quantite_demandee != 0:
            avancement_prod = cumul_entrees / quantite_demandee

        avancement_temps = None
        if duree_prevue and duree_prevue != 0:
            avancement_temps = cumul_temps_passes / duree_prevue

        result_list.append({
            "PRODUIT": row[0],
            "CATEGORIE": row[1],
            "DUREE_PREVUE": duree_prevue,
            "CUMUL_TEMPS_PASSES": cumul_temps_passes,
            "CUMUL_ENTREES": cumul_entrees,
            "LANCEMENT_AU_PLUS_TARD": lancement_au_plus_tard,
            "AFFAIRE": row[6],
            "NUMERO_OFDA": row[7],
            "STATUT": row[8],
            "LANCE_LE": row[9],
            "DESIGNATION": row[10],
            "QUANTITE_DEMANDEE": quantite_demandee,
            "Semaine": semaine,
            "Avancement_Prod": avancement_prod,
            "Avancement_Temps": avancement_temps,
        })

    cursor.close()
    conn.close()
    return SuccessResponse(data={"of_list": result_list, "count": len(result_list)})


@router.get("/filtered", response_model=SuccessResponse)
def get_filtered_of_data(
    date_debut: Optional[str] = Query(None, description="Start date (YYYY-MM-DD)"),
    date_fin: Optional[str] = Query(None, description="End date (YYYY-MM-DD)"),
    statut_filter: Optional[str] = Query(None, description="Status filter (C/T/A)"),
    famille_filter: Optional[str] = Query(None, description="Family filter"),
    client_filter: Optional[str] = Query(None, description="Client filter"),
    alerte_filter: Optional[bool] = Query(None, description="Alert filter"),
    analyzer=Depends(get_analyzer)
):
    """
    Get filtered OF data using the analyzer.
    This extends your constructor pattern with additional filtering capabilities.
    """
    try:
        # Set default dates if not provided
        if not date_debut:
            from datetime import datetime, timedelta
            date_debut = (datetime.now() - timedelta(days=90)).strftime('%Y-%m-%d')
        if not date_fin:
            from datetime import datetime
            date_fin = datetime.now().strftime('%Y-%m-%d')
        
        # Get main OF data
        df = analyzer.get_comprehensive_of_data(date_debut, date_fin, statut_filter)
        
        if df is not None and not df.empty:
            # Apply additional filters
            if famille_filter and famille_filter != "Toutes":
                df = df[df['FAMILLE_TECHNIQUE'] == famille_filter]
            if client_filter and client_filter != "Tous":
                df = df[df['CLIENT'] == client_filter]
            if alerte_filter is not None:
                df = df[df['Alerte_temps'] == alerte_filter]
        
        # Convert DataFrame to list of dictionaries
        data = df.fillna('').to_dict('records') if df is not None else []
        
        return SuccessResponse(data={
            "of_list": data,
            "count": len(data),
            "filters": {
                "date_debut": date_debut,
                "date_fin": date_fin,
                "statut_filter": statut_filter,
                "famille_filter": famille_filter,
                "client_filter": client_filter,
                "alerte_filter": alerte_filter
            }
        })
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching filtered OF data: {str(e)}")


@router.get("/by_status/{status}", response_model=SuccessResponse)
def get_of_by_status(status: str):
    """
    Get OF data by specific status.
    Extends your constructor pattern for status-specific queries.
    """
    conn = get_db_connection()
    cursor = conn.cursor()
    
    query = """
    SELECT 
        OF_DA.NUMERO_OFDA,
        OF_DA.PRODUIT,
        OF_DA.STATUT,
        OF_DA.LANCEMENT_AU_PLUS_TARD,
        OF_DA.QUANTITE_DEMANDEE,
        OF_DA.CUMUL_ENTREES,
        OF_DA.DUREE_PREVUE,
        OF_DA.CUMUL_TEMPS_PASSES,
        OF_DA.AFFAIRE,
        OF_DA.DESIGNATION,
        OF_DA.LANCE_LE
    FROM gpao.OF_DA OF_DA
    WHERE OF_DA.NUMERO_OFDA LIKE 'F%' AND OF_DA.STATUT = ?
    ORDER BY OF_DA.LANCEMENT_AU_PLUS_TARD DESC
    """
    
    cursor.execute(query, (status,))
    rows = cursor.fetchall()
    of_list = []

    for row in rows:
        # Parse LANCEMENT_AU_PLUS_TARD for Semaine calculation
        lancement_au_plus_tard = row[3]
        if isinstance(lancement_au_plus_tard, datetime):
            semaine = lancement_au_plus_tard.isocalendar()[1]
        else:
            semaine = None

        # Calculating Avancement_Prod safely
        quantite_demandee = row[4]
        cumul_entrees = row[5]
        if quantite_demandee and quantite_demandee != 0:
            avancement_prod = cumul_entrees / quantite_demandee
        else:
            avancement_prod = None

        # Calculating Avancement_Temps safely
        duree_prevue = row[6]
        cumul_temps_passes = row[7]
        if duree_prevue and duree_prevue != 0:
            avancement_temps = cumul_temps_passes / duree_prevue
        else:
            avancement_temps = None

        of_list.append({
            "NUMERO_OFDA": row[0],
            "PRODUIT": row[1],
            "STATUT": row[2],
            "LANCEMENT_AU_PLUS_TARD": row[3],
            "QUANTITE_DEMANDEE": row[4],
            "CUMUL_ENTREES": row[5],
            "DUREE_PREVUE": row[6],
            "CUMUL_TEMPS_PASSES": row[7],
            "AFFAIRE": row[8],
            "DESIGNATION": row[9],
            "LANCE_LE": row[10],
            "Semaine": semaine,
            "Avancement_Prod": avancement_prod,
            "Avancement_Temps": avancement_temps,
        })

    cursor.close()
    conn.close()
    return SuccessResponse(data={"of_list": of_list, "count": len(of_list), "status": status})
