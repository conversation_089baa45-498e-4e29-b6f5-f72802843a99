<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🏭 Gestion OF - Excalibur ERP</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
        .metric-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        .metric-value {
            font-size: 2rem;
            font-weight: bold;
        }
        .metric-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }
        .sidebar {
            background-color: #f8f9fa;
            min-height: 100vh;
            padding: 20px;
        }
        .main-content {
            padding: 20px;
        }
        .table-container {
            max-height: 600px;
            overflow-y: auto;
        }
        .nav-tabs .nav-link.active {
            background-color: #667eea;
            color: white;
            border-color: #667eea;
        }
        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 sidebar">
                <h4 class="mb-4">
                    <i class="fas fa-cogs"></i> Navigation
                </h4>
                
                <div class="list-group mb-3">
                    <a href="/" class="list-group-item list-group-item-action">
                        <i class="fas fa-tachometer-alt"></i> Dashboard Principal
                    </a>
                    <a href="#" class="list-group-item list-group-item-action active">
                        <i class="fas fa-list"></i> Gestion OF
                    </a>
                </div>
                
                <div class="card mb-3">
                    <div class="card-header">
                        <i class="fas fa-filter"></i> Filtres
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="statusFilter" class="form-label">Statut</label>
                            <select class="form-select" id="statusFilter">
                                <option value="">Tous</option>
                                <option value="C">En Cours (C)</option>
                                <option value="T">Terminés (T)</option>
                                <option value="A">Arrêtés (A)</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="searchInput" class="form-label">Recherche</label>
                            <input type="text" class="form-control" id="searchInput" placeholder="N° OF, Produit...">
                        </div>
                    </div>
                </div>
                
                <div class="card mb-3">
                    <div class="card-header">
                        <i class="fas fa-download"></i> Actions
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <button class="btn btn-outline-success btn-sm" onclick="exportOFData('csv')">
                                <i class="fas fa-file-csv"></i> Export CSV
                            </button>
                            <button class="btn btn-outline-info btn-sm" onclick="exportOFData('excel')">
                                <i class="fas fa-file-excel"></i> Export Excel
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-9 main-content">
                <h1 class="mb-4">
                    🏭 Gestion des Ordres de Fabrication
                </h1>
                <p class="text-muted">Consultation et analyse des OF en temps réel</p>
                <hr>
                
                <!-- Loading indicator -->
                <div class="loading" id="loadingIndicator">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Chargement...</span>
                    </div>
                    <p>Chargement des données...</p>
                </div>
                
                <!-- Status alerts -->
                <div class="alert alert-success" id="successAlert" style="display: none;">
                    <i class="fas fa-check-circle"></i> <span id="successMessage"></span>
                </div>
                
                <div class="alert alert-danger" id="errorAlert" style="display: none;">
                    <i class="fas fa-exclamation-triangle"></i> <span id="errorMessage"></span>
                </div>
                
                <!-- Quick Stats -->
                <div class="row mb-4" id="quickStats">
                    <!-- Stats will be populated by JavaScript -->
                </div>
                
                <!-- Tabs for different OF views -->
                <ul class="nav nav-tabs" id="ofTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="en-cours-tab" data-bs-toggle="tab" 
                                data-bs-target="#en-cours" type="button" role="tab">
                            <i class="fas fa-play"></i> OF En Cours
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="histo-tab" data-bs-toggle="tab" 
                                data-bs-target="#histo" type="button" role="tab">
                            <i class="fas fa-history"></i> Historique
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="all-tab" data-bs-toggle="tab" 
                                data-bs-target="#all" type="button" role="tab">
                            <i class="fas fa-list"></i> Tous les OF
                        </button>
                    </li>
                </ul>
                
                <div class="tab-content" id="ofTabContent">
                    <div class="tab-pane fade show active" id="en-cours" role="tabpanel">
                        <div class="mt-3">
                            <h4>📋 OF En Cours de Production</h4>
                            <div class="table-container">
                                <table class="table table-striped table-hover">
                                    <thead class="table-dark" id="enCoursHeader">
                                        <!-- Header will be populated by JavaScript -->
                                    </thead>
                                    <tbody id="enCoursBody">
                                        <!-- Data will be populated by JavaScript -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    
                    <div class="tab-pane fade" id="histo" role="tabpanel">
                        <div class="mt-3">
                            <h4>📚 Historique des OF</h4>
                            <div class="table-container">
                                <table class="table table-striped table-hover">
                                    <thead class="table-dark" id="histoHeader">
                                        <!-- Header will be populated by JavaScript -->
                                    </thead>
                                    <tbody id="histoBody">
                                        <!-- Data will be populated by JavaScript -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    
                    <div class="tab-pane fade" id="all" role="tabpanel">
                        <div class="mt-3">
                            <h4>📊 Tous les OF (Combiné)</h4>
                            <div class="table-container">
                                <table class="table table-striped table-hover">
                                    <thead class="table-dark" id="allHeader">
                                        <!-- Header will be populated by JavaScript -->
                                    </thead>
                                    <tbody id="allBody">
                                        <!-- Data will be populated by JavaScript -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JavaScript -->
    <script src="/static/of_management.js"></script>
</body>
</html>
