"""
Pydantic models for OF (Ordre de Fabrication) data.
"""

from pydantic import BaseModel
from typing import Optional, List
from datetime import datetime


class OFBase(BaseModel):
    """Base OF model."""
    numero_ofda: str
    produit: str
    statut: str
    designation: Optional[str] = None
    affaire: Optional[str] = None


class OFEnCours(OFBase):
    """OF En Cours model - matches your of_routes.py /en_cours endpoint."""
    lancement_au_plus_tard: Optional[datetime] = None
    quantite_demandee: Optional[float] = None
    cumul_entrees: Optional[float] = None
    duree_prevue: Optional[float] = None
    cumul_temps_passes: Optional[float] = None
    lance_le: Optional[datetime] = None
    semaine: Optional[int] = None
    avancement_prod: Optional[float] = None
    avancement_temps: Optional[float] = None


class OFHisto(OFBase):
    """OF Historique model - matches your of_routes.py /histo endpoint."""
    categorie: Optional[str] = None
    duree_prevue: Optional[float] = None
    cumul_temps_passes: Optional[float] = None
    cumul_entrees: Optional[float] = None
    lancement_au_plus_tard: Optional[datetime] = None
    lance_le: Optional[datetime] = None
    quantite_demandee: Optional[float] = None
    semaine: Optional[int] = None
    avancement_prod: Optional[float] = None
    avancement_temps: Optional[float] = None


class OFAll(BaseModel):
    """Combined OF model - matches your of_routes.py /all endpoint."""
    produit: str
    categorie: Optional[str] = None
    duree_prevue: Optional[float] = None
    cumul_temps_passes: Optional[float] = None
    cumul_entrees: Optional[float] = None
    lancement_au_plus_tard: Optional[datetime] = None
    affaire: Optional[str] = None
    numero_ofda: str
    statut: str
    lance_le: Optional[datetime] = None
    designation: Optional[str] = None
    quantite_demandee: Optional[float] = None
    semaine: Optional[int] = None
    avancement_prod: Optional[float] = None
    avancement_temps: Optional[float] = None


class OFFilters(BaseModel):
    """OF filtering parameters."""
    date_debut: Optional[str] = None
    date_fin: Optional[str] = None
    statut_filter: Optional[str] = None
    famille_filter: Optional[str] = None
    client_filter: Optional[str] = None
    alerte_filter: Optional[bool] = None


class OFResponse(BaseModel):
    """OF API response."""
    success: bool
    data: List[dict]
    count: int
    filters: Optional[OFFilters] = None
