version: '3.8'

services:
  fastapi-production-tracker:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DB_UID=${DB_UID:-gpao}
      - DB_PWD=${DB_PWD:-flat}
      - DB_HOST=${DB_HOST:-*************:2638}
      - DB_SERVER_NAME=${DB_SERVER_NAME:-excalib}
      - DB_DATABASE_NAME=${DB_DATABASE_NAME:-excalib}
    volumes:
      - ./.env:/app/.env:ro
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - production-tracker-network

networks:
  production-tracker-network:
    driver: bridge
