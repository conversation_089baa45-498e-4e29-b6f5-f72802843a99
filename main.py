#!/usr/bin/env python3
"""
FastAPI Application for Production Time Tracking - Excalibur ERP
Refactored from Streamlit to FastAPI for better scalability and hosting flexibility.
"""

from fastapi import FastAPI
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from pathlib import Path
from contextlib import asynccontextmanager

# Import our modular components
from app.core.config import get_settings
from app.core.database import init_analyzer, cleanup_analyzer
from app.routes import (
    of_routes,
    dashboard_routes,
    charge_routes,
    backlog_routes,
    personnel_routes,
    export_routes,
    health_routes
)

# Global analyzer instance (will be initialized on startup)
analyzer = None

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Initialize the data analyzer on startup."""
    global analyzer
    try:
        analyzer = init_analyzer()
        print("✅ Data analyzer initialized successfully")
    except Exception as e:
        print(f"❌ Failed to initialize data analyzer: {e}")
        analyzer = None
    yield
    # Cleanup on shutdown
    cleanup_analyzer()

# Create FastAPI app with lifespan
settings = get_settings()
app = FastAPI(
    title=settings.app_name,
    description=settings.app_description,
    version=settings.app_version,
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# Setup static files and templates
static_dir = Path("static")
templates_dir = Path("templates")

# Create directories if they don't exist
static_dir.mkdir(exist_ok=True)
templates_dir.mkdir(exist_ok=True)

app.mount("/static", StaticFiles(directory="static"), name="static")
templates = Jinja2Templates(directory="templates")

# Include all route modules
app.include_router(of_routes.router)
app.include_router(dashboard_routes.router)
app.include_router(charge_routes.router)
app.include_router(backlog_routes.router)
app.include_router(personnel_routes.router)
app.include_router(export_routes.router)
app.include_router(health_routes.router)

# All routes are now handled by the modular route system above

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000, reload=True)
