#!/usr/bin/env python3
"""
Test script for the comprehensive routing system.
This script tests all the major routes to ensure they work correctly.
"""

import requests
import json
import sys
from datetime import datetime, timedelta

# Base URL for the API
BASE_URL = "http://localhost:8000"

def test_route(endpoint, description, params=None):
    """Test a single route and return the result."""
    try:
        url = f"{BASE_URL}{endpoint}"
        if params:
            response = requests.get(url, params=params)
        else:
            response = requests.get(url)
        
        if response.status_code == 200:
            data = response.json()
            success = data.get('success', True) if isinstance(data, dict) else True
            if success:
                print(f"✅ {description}: SUCCESS")
                return True
            else:
                print(f"❌ {description}: API returned success=false")
                return False
        else:
            print(f"❌ {description}: HTTP {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print(f"❌ {description}: Connection failed (is the server running?)")
        return False
    except Exception as e:
        print(f"❌ {description}: {str(e)}")
        return False

def main():
    """Run all route tests."""
    print("🚀 Testing Comprehensive Routing System")
    print("=" * 60)
    
    # Check if server is running
    try:
        response = requests.get(f"{BASE_URL}/api/health/")
        if response.status_code != 200:
            print("❌ Server is not responding. Please start the FastAPI server first.")
            print("   Run: python main.py")
            sys.exit(1)
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to server. Please start the FastAPI server first.")
        print("   Run: python main.py")
        sys.exit(1)
    
    print("✅ Server is running")
    print()
    
    # Test results tracking
    total_tests = 0
    passed_tests = 0
    
    # Date parameters for testing
    date_debut = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
    date_fin = datetime.now().strftime('%Y-%m-%d')
    
    # Test OF Routes
    print("📋 Testing OF Routes...")
    tests = [
        ("/api/of/en_cours", "OF En Cours"),
        ("/api/of/histo", "OF Historique"),
        ("/api/of/all", "OF All Data"),
        ("/api/of/filtered", "OF Filtered", {"statut_filter": "C"}),
        ("/api/of/by_status/C", "OF By Status (C)"),
    ]
    
    for test in tests:
        total_tests += 1
        if len(test) == 3:
            if test_route(test[0], test[1], test[2]):
                passed_tests += 1
        else:
            if test_route(test[0], test[1]):
                passed_tests += 1
    
    print()
    
    # Test Dashboard Routes
    print("📊 Testing Dashboard Routes...")
    tests = [
        ("/", "Main Dashboard Page"),
        ("/api/dashboard-data", "Dashboard Data", {"date_debut": date_debut, "date_fin": date_fin}),
        ("/api/kpis", "KPIs"),
        ("/api/summary-stats", "Summary Stats"),
        ("/api/filters/options", "Filter Options"),
    ]
    
    for test in tests:
        total_tests += 1
        if len(test) == 3:
            if test_route(test[0], test[1], test[2]):
                passed_tests += 1
        else:
            if test_route(test[0], test[1]):
                passed_tests += 1
    
    print()
    
    # Test Charge Routes
    print("⚙️ Testing Charge Routes...")
    tests = [
        ("/api/charge/", "Charge Data"),
        ("/api/charge/by_sector", "Charge By Sector"),
        ("/api/charge/capacity", "Capacity Analysis"),
        ("/api/charge/overload", "Overload Analysis"),
        ("/api/charge/efficiency", "Efficiency Analysis"),
        ("/api/charge/planning", "Planning Data"),
    ]
    
    for test in tests:
        total_tests += 1
        if test_route(test[0], test[1]):
            passed_tests += 1
    
    print()
    
    # Test Backlog Routes
    print("📋 Testing Backlog Routes...")
    tests = [
        ("/api/backlog/", "Backlog Data"),
        ("/api/backlog/urgent", "Urgent Backlog"),
        ("/api/backlog/by_priority/URGENT", "Backlog By Priority"),
        ("/api/backlog/summary", "Backlog Summary"),
        ("/api/backlog/overdue", "Overdue Orders"),
    ]
    
    for test in tests:
        total_tests += 1
        if test_route(test[0], test[1]):
            passed_tests += 1
    
    print()
    
    # Test Personnel Routes
    print("👥 Testing Personnel Routes...")
    tests = [
        ("/api/personnel/", "Personnel Data"),
        ("/api/personnel/by_sector/CMS", "Personnel By Sector"),
        ("/api/personnel/qualifications", "Qualifications"),
        ("/api/personnel/efficiency", "Personnel Efficiency"),
        ("/api/personnel/summary", "Personnel Summary"),
        ("/api/personnel/search", "Personnel Search", {"secteur": "CMS"}),
    ]
    
    for test in tests:
        total_tests += 1
        if len(test) == 3:
            if test_route(test[0], test[1], test[2]):
                passed_tests += 1
        else:
            if test_route(test[0], test[1]):
                passed_tests += 1
    
    print()
    
    # Test Export Routes
    print("📤 Testing Export Routes...")
    tests = [
        ("/api/export/formats", "Export Formats"),
        # Note: CSV exports are not tested to avoid large downloads
    ]
    
    for test in tests:
        total_tests += 1
        if test_route(test[0], test[1]):
            passed_tests += 1
    
    print()
    
    # Test Health Routes
    print("🏥 Testing Health Routes...")
    tests = [
        ("/api/health/", "Basic Health Check"),
        ("/api/health/detailed", "Detailed Health Check"),
        ("/api/health/database", "Database Health"),
        ("/api/health/data-sources", "Data Sources Check"),
        ("/api/health/performance", "Performance Check"),
        ("/api/health/version", "Version Info"),
    ]
    
    for test in tests:
        total_tests += 1
        if test_route(test[0], test[1]):
            passed_tests += 1
    
    print()
    
    # Summary
    print("=" * 60)
    print(f"📊 Test Results: {passed_tests}/{total_tests} tests passed")
    
    if passed_tests == total_tests:
        print("🎉 All tests passed! The routing system is working correctly.")
        return True
    else:
        failed_tests = total_tests - passed_tests
        print(f"⚠️ {failed_tests} test(s) failed. Check the server logs for details.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
