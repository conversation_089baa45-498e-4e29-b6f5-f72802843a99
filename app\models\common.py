"""
Common Pydantic models for API responses.
"""

from pydantic import BaseModel
from typing import Optional, List, Dict, Any
from datetime import datetime


class BaseResponse(BaseModel):
    """Base response model."""
    success: bool
    message: Optional[str] = None


class ErrorResponse(BaseResponse):
    """Error response model."""
    success: bool = False
    error: str
    detail: Optional[str] = None


class SuccessResponse(BaseResponse):
    """Success response model."""
    success: bool = True
    data: Optional[Dict[str, Any]] = None


class PaginatedResponse(BaseResponse):
    """Paginated response model."""
    success: bool = True
    data: List[Dict[str, Any]]
    total: int
    page: int = 1
    page_size: int = 50
    total_pages: int


class FilterOptions(BaseModel):
    """Filter options for dropdowns."""
    familles: List[str]
    clients: List[str]
    statuts: List[str]


class DateRange(BaseModel):
    """Date range model."""
    date_debut: Optional[str] = None
    date_fin: Optional[str] = None


class KPIData(BaseModel):
    """KPI data model."""
    total_of: int
    of_en_cours: int
    of_termines: int
    of_arretes: int
    avg_prod: float
    avg_temps: float
    alertes: int
    efficacite: float
