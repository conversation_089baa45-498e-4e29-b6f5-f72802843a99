"""
Export routes for data export functionality (CSV, Excel, PDF, etc.).
"""

from fastapi import APIRouter, HTTPException, Depends, Query
from fastapi.responses import StreamingResponse
from typing import Optional
from datetime import datetime, timedelta
import pandas as pd
import io

from app.core.database import get_analyzer
from app.models.common import SuccessResponse

router = APIRouter(prefix="/api/export", tags=["Export"])


@router.get("/csv/of")
async def export_of_csv(
    date_debut: Optional[str] = Query(None, description="Start date (YYYY-MM-DD)"),
    date_fin: Optional[str] = Query(None, description="End date (YYYY-MM-DD)"),
    statut_filter: Optional[str] = Query(None, description="Status filter (C/T/A)"),
    analyzer=Depends(get_analyzer)
):
    """Export OF data as CSV."""
    try:
        # Set default dates if not provided
        if not date_debut:
            date_debut = (datetime.now() - timedelta(days=90)).strftime('%Y-%m-%d')
        if not date_fin:
            date_fin = datetime.now().strftime('%Y-%m-%d')

        # Get main OF data
        df = analyzer.get_comprehensive_of_data(date_debut, date_fin, statut_filter)

        if df is None or df.empty:
            raise HTTPException(status_code=404, detail="No data found for export")

        # Convert to CSV
        output = io.StringIO()
        df.to_csv(output, index=False, encoding='utf-8')
        output.seek(0)

        # Create filename
        filename = f"export_of_{date_debut}_to_{date_fin}.csv"

        return StreamingResponse(
            io.BytesIO(output.getvalue().encode('utf-8')),
            media_type="text/csv",
            headers={"Content-Disposition": f"attachment; filename={filename}"}
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error exporting OF data: {str(e)}")


@router.get("/csv/charge")
async def export_charge_csv(
    date_debut: Optional[str] = Query(None, description="Start date (YYYY-MM-DD)"),
    date_fin: Optional[str] = Query(None, description="End date (YYYY-MM-DD)"),
    analyzer=Depends(get_analyzer)
):
    """Export charge data as CSV."""
    try:
        # Set default dates if not provided
        if not date_debut:
            date_debut = (datetime.now() - timedelta(days=90)).strftime('%Y-%m-%d')
        if not date_fin:
            date_fin = datetime.now().strftime('%Y-%m-%d')

        # Get charge data
        df = analyzer.get_charge_travail_data(date_debut, date_fin)

        if df is None or df.empty:
            raise HTTPException(status_code=404, detail="No charge data found for export")

        # Convert to CSV
        output = io.StringIO()
        df.to_csv(output, index=False, encoding='utf-8')
        output.seek(0)

        # Create filename
        filename = f"export_charge_{date_debut}_to_{date_fin}.csv"

        return StreamingResponse(
            io.BytesIO(output.getvalue().encode('utf-8')),
            media_type="text/csv",
            headers={"Content-Disposition": f"attachment; filename={filename}"}
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error exporting charge data: {str(e)}")


@router.get("/csv/backlog")
async def export_backlog_csv(
    date_debut: Optional[str] = Query(None, description="Start date (YYYY-MM-DD)"),
    date_fin: Optional[str] = Query(None, description="End date (YYYY-MM-DD)"),
    analyzer=Depends(get_analyzer)
):
    """Export backlog data as CSV."""
    try:
        # Set default dates if not provided
        if not date_debut:
            date_debut = (datetime.now() - timedelta(days=90)).strftime('%Y-%m-%d')
        if not date_fin:
            date_fin = datetime.now().strftime('%Y-%m-%d')

        # Get backlog data
        df = analyzer.get_backlog_data(date_debut, date_fin)

        if df is None or df.empty:
            raise HTTPException(status_code=404, detail="No backlog data found for export")

        # Convert to CSV
        output = io.StringIO()
        df.to_csv(output, index=False, encoding='utf-8')
        output.seek(0)

        # Create filename
        filename = f"export_backlog_{date_debut}_to_{date_fin}.csv"

        return StreamingResponse(
            io.BytesIO(output.getvalue().encode('utf-8')),
            media_type="text/csv",
            headers={"Content-Disposition": f"attachment; filename={filename}"}
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error exporting backlog data: {str(e)}")


@router.get("/csv/personnel")
async def export_personnel_csv(analyzer=Depends(get_analyzer)):
    """Export personnel data as CSV."""
    try:
        # Get personnel data
        df = analyzer.get_personnel_data()

        if df is None or df.empty:
            raise HTTPException(status_code=404, detail="No personnel data found for export")

        # Convert to CSV
        output = io.StringIO()
        df.to_csv(output, index=False, encoding='utf-8')
        output.seek(0)

        # Create filename
        filename = f"export_personnel_{datetime.now().strftime('%Y%m%d')}.csv"

        return StreamingResponse(
            io.BytesIO(output.getvalue().encode('utf-8')),
            media_type="text/csv",
            headers={"Content-Disposition": f"attachment; filename={filename}"}
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error exporting personnel data: {str(e)}")


@router.get("/csv/dashboard")
async def export_dashboard_csv(
    date_debut: Optional[str] = Query(None, description="Start date (YYYY-MM-DD)"),
    date_fin: Optional[str] = Query(None, description="End date (YYYY-MM-DD)"),
    statut_filter: Optional[str] = Query(None, description="Status filter (C/T/A)"),
    analyzer=Depends(get_analyzer)
):
    """Export complete dashboard data as CSV (multiple sheets in one file)."""
    try:
        # Set default dates if not provided
        if not date_debut:
            date_debut = (datetime.now() - timedelta(days=90)).strftime('%Y-%m-%d')
        if not date_fin:
            date_fin = datetime.now().strftime('%Y-%m-%d')

        # Get all dashboard data
        dashboard_data = analyzer.get_dashboard_data(date_debut, date_fin, statut_filter)

        # Create a combined CSV with sections
        output = io.StringIO()
        
        # Write header
        output.write(f"# Dashboard Export - {date_debut} to {date_fin}\n")
        output.write(f"# Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        output.write(f"# Status filter: {statut_filter or 'All'}\n\n")

        # Write OF data
        main_of_data = dashboard_data.get('main_of_data')
        if main_of_data is not None and not main_of_data.empty:
            output.write("=== OF DATA ===\n")
            main_of_data.to_csv(output, index=False)
            output.write("\n")

        # Write charge data
        charge_data = dashboard_data.get('charge_data')
        if charge_data is not None and not charge_data.empty:
            output.write("=== CHARGE DATA ===\n")
            charge_data.to_csv(output, index=False)
            output.write("\n")

        # Write backlog data
        backlog_data = dashboard_data.get('backlog_data')
        if backlog_data is not None and not backlog_data.empty:
            output.write("=== BACKLOG DATA ===\n")
            backlog_data.to_csv(output, index=False)
            output.write("\n")

        # Write personnel data
        personnel_data = dashboard_data.get('personnel_data')
        if personnel_data is not None and not personnel_data.empty:
            output.write("=== PERSONNEL DATA ===\n")
            personnel_data.to_csv(output, index=False)

        output.seek(0)

        # Create filename
        filename = f"export_dashboard_{date_debut}_to_{date_fin}.csv"

        return StreamingResponse(
            io.BytesIO(output.getvalue().encode('utf-8')),
            media_type="text/csv",
            headers={"Content-Disposition": f"attachment; filename={filename}"}
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error exporting dashboard data: {str(e)}")


@router.get("/report/text")
async def export_text_report(
    date_debut: Optional[str] = Query(None, description="Start date (YYYY-MM-DD)"),
    date_fin: Optional[str] = Query(None, description="End date (YYYY-MM-DD)"),
    statut_filter: Optional[str] = Query(None, description="Status filter (C/T/A)"),
    analyzer=Depends(get_analyzer)
):
    """Generate and export a text report."""
    try:
        # Set default dates if not provided
        if not date_debut:
            date_debut = (datetime.now() - timedelta(days=90)).strftime('%Y-%m-%d')
        if not date_fin:
            date_fin = datetime.now().strftime('%Y-%m-%d')

        # Get main OF data
        df = analyzer.get_comprehensive_of_data(date_debut, date_fin, statut_filter)

        if df is None or df.empty:
            report_text = "Aucune donnée disponible pour générer le rapport."
        else:
            # Generate report
            report_text = analyzer.generate_summary_report(df)

        # Create filename
        filename = f"rapport_{date_debut}_to_{date_fin}.txt"

        return StreamingResponse(
            io.BytesIO(report_text.encode('utf-8')),
            media_type="text/plain",
            headers={"Content-Disposition": f"attachment; filename={filename}"}
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error generating text report: {str(e)}")


@router.get("/formats", response_model=SuccessResponse)
async def get_export_formats():
    """Get available export formats and their descriptions."""
    formats = {
        "csv": {
            "description": "Comma Separated Values - Compatible with Excel and other spreadsheet applications",
            "mime_type": "text/csv",
            "extension": ".csv",
            "available_data": ["of", "charge", "backlog", "personnel", "dashboard"]
        },
        "text_report": {
            "description": "Text summary report with analysis and recommendations",
            "mime_type": "text/plain",
            "extension": ".txt",
            "available_data": ["summary_report"]
        }
    }
    
    return SuccessResponse(data={
        "formats": formats,
        "endpoints": {
            "of_csv": "/api/export/csv/of",
            "charge_csv": "/api/export/csv/charge",
            "backlog_csv": "/api/export/csv/backlog",
            "personnel_csv": "/api/export/csv/personnel",
            "dashboard_csv": "/api/export/csv/dashboard",
            "text_report": "/api/export/report/text"
        }
    })
