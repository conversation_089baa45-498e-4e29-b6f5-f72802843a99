// Dashboard JavaScript functionality
let currentData = null;
let filterOptions = null;

// API Configuration
const API_BASE = ""; // Since we're serving from the same domain
const API_ENDPOINTS = {
  dashboard: "/api/dashboard-data",
  kpis: "/api/kpis",
  filters: "/api/filters/options",
  of: {
    en_cours: "/api/of/en_cours",
    histo: "/api/of/histo",
    all: "/api/of/all",
    filtered: "/api/of/filtered",
    by_status: "/api/of/by_status",
  },
  charge: {
    base: "/api/charge/",
    by_sector: "/api/charge/by_sector",
    capacity: "/api/charge/capacity",
    overload: "/api/charge/overload",
    efficiency: "/api/charge/efficiency",
  },
  backlog: {
    base: "/api/backlog/",
    urgent: "/api/backlog/urgent",
    by_priority: "/api/backlog/by_priority",
    summary: "/api/backlog/summary",
    overdue: "/api/backlog/overdue",
  },
  personnel: {
    base: "/api/personnel/",
    by_sector: "/api/personnel/by_sector",
  },
  export: {
    csv: "/api/export/csv",
    excel: "/api/export/excel",
    dashboard: "/api/export/dashboard",
  },
  health: {
    base: "/api/health/",
    database: "/api/health/database",
    performance: "/api/health/performance",
  },
};

// Initialize dashboard on page load
document.addEventListener("DOMContentLoaded", function () {
  // Log Plotly version for verification
  if (typeof Plotly !== 'undefined') {
    console.log('✅ Plotly.js loaded successfully, version:', Plotly.BUILD);
  } else {
    console.error('❌ Plotly.js failed to load');
  }

  initializeDates();
  loadFilterOptions();
  refreshData();
});

function initializeDates() {
  const today = new Date();
  const threeMonthsAgo = new Date(today.getTime() - 90 * 24 * 60 * 60 * 1000);

  document.getElementById("dateFin").value = today.toISOString().split("T")[0];
  document.getElementById("dateDebut").value = threeMonthsAgo
    .toISOString()
    .split("T")[0];
}

function showLoading() {
  document.getElementById("loadingIndicator").style.display = "block";
  document.getElementById("errorAlert").style.display = "none";
}

function hideLoading() {
  document.getElementById("loadingIndicator").style.display = "none";
}

function showError(message) {
  document.getElementById("errorMessage").textContent = message;
  document.getElementById("errorAlert").style.display = "block";
  hideLoading();
}

function showConnectionStatus() {
  document.getElementById("connectionStatus").style.display = "block";
  updateLastRefreshTime();
}

function showInfo(message) {
  document.getElementById("infoMessage").textContent = message;
  document.getElementById("infoAlert").style.display = "block";
  setTimeout(() => {
    document.getElementById("infoAlert").style.display = "none";
  }, 5000);
}

function updateLastRefreshTime() {
  const now = new Date();
  document.getElementById("lastRefresh").textContent = now.toLocaleTimeString();
  document.getElementById(
    "lastUpdateTime"
  ).textContent = `Mis à jour: ${now.toLocaleTimeString()}`;
}

function updateDataCount(count) {
  document.getElementById("dataCount").textContent = count || 0;
}

// Load filter options from API
async function loadFilterOptions() {
  try {
    const response = await fetch(API_ENDPOINTS.filters);
    if (response.ok) {
      const data = await response.json();
      if (data.success) {
        filterOptions = data.data;
        populateFilterDropdowns();
      }
    }
  } catch (error) {
    console.warn("Could not load filter options:", error);
  }
}

function populateFilterDropdowns() {
  if (!filterOptions) return;

  // Populate famille filter
  const familleSelect = document.getElementById("familleFilter");
  if (filterOptions.familles) {
    familleSelect.innerHTML = '<option value="">Toutes</option>';
    filterOptions.familles.forEach((famille) => {
      const option = document.createElement("option");
      option.value = famille;
      option.textContent = famille;
      familleSelect.appendChild(option);
    });
  }

  // Populate client filter
  const clientSelect = document.getElementById("clientFilter");
  if (filterOptions.clients) {
    clientSelect.innerHTML = '<option value="">Tous</option>';
    filterOptions.clients.forEach((client) => {
      const option = document.createElement("option");
      option.value = client;
      option.textContent = client;
      clientSelect.appendChild(option);
    });
  }
}

async function refreshData() {
  showLoading();

  try {
    const dateDebut = document.getElementById("dateDebut").value;
    const dateFin = document.getElementById("dateFin").value;
    const statutFilter = document.getElementById("statutFilter").value;
    const familleFilter = document.getElementById("familleFilter").value;
    const clientFilter = document.getElementById("clientFilter").value;
    const alerteFilter = document.getElementById("alerteFilter").checked;

    const params = new URLSearchParams();
    if (dateDebut) params.append("date_debut", dateDebut);
    if (dateFin) params.append("date_fin", dateFin);
    if (statutFilter) params.append("statut_filter", statutFilter);
    if (familleFilter) params.append("famille_filter", familleFilter);
    if (clientFilter) params.append("client_filter", clientFilter);
    if (alerteFilter) params.append("alerte_filter", "true");

    const response = await fetch(`${API_ENDPOINTS.dashboard}?${params}`);

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();

    console.log("Received data:", data); // Debug log

    if (data.success) {
      currentData = data;
      showConnectionStatus();

      // Update data count - the data structure has nested data
      const nestedData = data.data?.data || data.data;
      const totalRecords =
        (nestedData?.main_of?.length || 0) +
        (nestedData?.charge?.length || 0) +
        (nestedData?.backlog?.length || 0) +
        (nestedData?.personnel?.length || 0);
      updateDataCount(totalRecords);

      // Debug log for KPIs - check both possible locations
      console.log("Full response data:", data);
      console.log("KPIs data (direct):", data.kpis);
      console.log("KPIs data (nested):", data.data?.kpis);

      // Check if kpis exists - try both locations
      const kpis = data.kpis || data.data?.kpis;
      if (kpis) {
        renderKPIs(kpis);
      } else {
        console.error("No KPIs data found in response");
        console.error("Response structure:", Object.keys(data));
        if (data.data) {
          console.error("data.data structure:", Object.keys(data.data));
        }
        const kpiContainer = document.getElementById("kpiCards");
        if (kpiContainer) {
          kpiContainer.innerHTML =
            '<div class="alert alert-warning">Aucune donnée KPI disponible dans la réponse</div>';
        }
      }

      renderTabs();
      hideLoading();

      // Show success message
      showInfo(
        `Données mises à jour avec succès (${totalRecords} enregistrements)`
      );
    } else {
      throw new Error(data.message || "Failed to fetch data");
    }
  } catch (error) {
    console.error("Error fetching data:", error);
    showError("Erreur lors du chargement des données: " + error.message);
  }
}

function renderKPIs(kpis) {
  const kpiContainer = document.getElementById("kpiCards");

  // Add defensive programming to handle undefined/null kpis
  if (!kpis) {
    console.error("KPIs data is undefined or null");
    kpiContainer.innerHTML =
      '<div class="alert alert-warning">Aucune donnée KPI disponible</div>';
    return;
  }

  // Provide default values for missing properties
  const safeKpis = {
    total_of: kpis.total_of || 0,
    of_en_cours: kpis.of_en_cours || 0,
    of_termines: kpis.of_termines || 0,
    of_arretes: kpis.of_arretes || 0,
    avg_prod: kpis.avg_prod || 0,
    avg_temps: kpis.avg_temps || 0,
    alertes: kpis.alertes || 0,
    efficacite: kpis.efficacite || 0,
  };

  const kpiCards = [
    {
      label: "Total OF Analysés",
      value: safeKpis.total_of,
      class: "info-card",
      icon: "fas fa-list",
    },
    {
      label: "OF en Cours",
      value: safeKpis.of_en_cours,
      class: "warning-card",
      icon: "fas fa-play",
    },
    {
      label: "OF Terminés",
      value: safeKpis.of_termines,
      class: "success-card",
      icon: "fas fa-check",
    },
    {
      label: "OF Arrêtés",
      value: safeKpis.of_arretes,
      class: "alert-card",
      icon: "fas fa-stop",
    },
    {
      label: "Avanc. Prod. Moyen",
      value: `${safeKpis.avg_prod.toFixed(1)}%`,
      class: "info-card",
      icon: "fas fa-chart-line",
    },
    {
      label: "Avanc. Temps Moyen",
      value: `${safeKpis.avg_temps.toFixed(1)}%`,
      class: "info-card",
      icon: "fas fa-clock",
    },
    {
      label: "⚠️ OF en Alerte Temps",
      value: safeKpis.alertes,
      class: "alert-card",
      icon: "fas fa-exclamation-triangle",
    },
    {
      label: "Efficacité Moyenne",
      value: safeKpis.efficacite.toFixed(2),
      class: "success-card",
      icon: "fas fa-tachometer-alt",
    },
  ];

  kpiContainer.innerHTML = kpiCards
    .map(
      (kpi) => `
        <div class="col-md-3 col-sm-6 mb-3">
            <div class="metric-card ${kpi.class}">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="metric-value">${kpi.value}</div>
                        <div class="metric-label">${kpi.label}</div>
                    </div>
                    <div>
                        <i class="${kpi.icon} fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    `
    )
    .join("");
}

function renderTabs() {
  const showOverview = document.getElementById("showOverview").checked;
  const showDetails = document.getElementById("showDetails").checked;
  const showCharts = document.getElementById("showCharts").checked;
  const showReport = document.getElementById("showReport").checked;
  const showCharge = document.getElementById("showCharge").checked;
  const showBacklog = document.getElementById("showBacklog").checked;
  const showPersonnel = document.getElementById("showPersonnel").checked;

  const tabs = [];
  const tabContent = [];

  // Check if currentData and currentData.data exist before accessing properties
  // Handle both nested and direct data structures
  const nestedData = currentData?.data?.data || currentData?.data;
  const hasMainOfData =
    nestedData && nestedData.main_of && nestedData.main_of.length > 0;
  const hasChargeData =
    nestedData && nestedData.charge && nestedData.charge.length > 0;
  const hasBacklogData =
    nestedData && nestedData.backlog && nestedData.backlog.length > 0;
  const hasPersonnelData =
    nestedData && nestedData.personnel && nestedData.personnel.length > 0;

  if (showOverview) {
    tabs.push({ id: "overview", label: "📊 Synthèse", active: true });
    tabContent.push({ id: "overview", content: renderOverviewTab() });
  }

  if (showDetails && hasMainOfData) {
    tabs.push({ id: "details", label: "📋 Détail OF", active: !showOverview });
    tabContent.push({ id: "details", content: renderDetailsTab() });
  }

  if (showCharts && hasMainOfData) {
    tabs.push({
      id: "charts",
      label: "📈 Graphiques",
      active: !showOverview && !showDetails,
    });
    tabContent.push({ id: "charts", content: renderChartsTab() });
  }

  if (showReport && hasMainOfData) {
    tabs.push({ id: "report", label: "📄 Rapport", active: false });
    tabContent.push({ id: "report", content: renderReportTab() });
  }

  if (showCharge && hasChargeData) {
    tabs.push({ id: "charge", label: "⚙️ Charge Travail", active: false });
    tabContent.push({ id: "charge", content: renderChargeTab() });
  }

  if (showBacklog && hasBacklogData) {
    tabs.push({ id: "backlog", label: "⏳ Backlog", active: false });
    tabContent.push({ id: "backlog", content: renderBacklogTab() });
  }

  if (showPersonnel && hasPersonnelData) {
    tabs.push({ id: "personnel", label: "👥 Personnel", active: false });
    tabContent.push({ id: "personnel", content: renderPersonnelTab() });
  }

  // Render tabs
  const tabsContainer = document.getElementById("mainTabs");
  tabsContainer.innerHTML = tabs
    .map(
      (tab) => `
        <li class="nav-item" role="presentation">
            <button class="nav-link ${tab.active ? "active" : ""}" 
                    id="${tab.id}-tab" 
                    data-bs-toggle="tab" 
                    data-bs-target="#${tab.id}" 
                    type="button" 
                    role="tab">
                ${tab.label}
            </button>
        </li>
    `
    )
    .join("");

  // Render tab content
  const contentContainer = document.getElementById("mainTabContent");
  contentContainer.innerHTML = tabContent
    .map(
      (content) => `
        <div class="tab-pane fade ${
          content.id === tabs.find((t) => t.active)?.id ? "show active" : ""
        }" 
             id="${content.id}" 
             role="tabpanel">
            ${content.content}
        </div>
    `
    )
    .join("");

  // Initialize charts after content is rendered
  setTimeout(() => {
    if (showCharts && hasMainOfData) {
      initializeCharts();
    }
  }, 100);
}

function renderOverviewTab() {
  const nestedData = currentData?.data?.data || currentData?.data;
  if (!nestedData || !nestedData.main_of) {
    return '<div class="alert alert-info">Aucune donnée OF disponible pour la synthèse.</div>';
  }

  const data = nestedData.main_of;

  if (data.length === 0) {
    return '<div class="alert alert-info">Aucune donnée OF disponible pour la synthèse.</div>';
  }

  return `
        <div class="mt-3">
            <h4>📊 Synthèse Globale de la Production</h4>
            <div class="row mt-4">
                <div class="col-md-6">
                    <div id="statusPieChart" style="height: 400px;"></div>
                </div>
                <div class="col-md-6">
                    <div id="alertsBarChart" style="height: 400px;"></div>
                </div>
            </div>
        </div>
    `;
}

function renderDetailsTab() {
  const nestedData = currentData?.data?.data || currentData?.data;
  if (!nestedData || !nestedData.main_of) {
    return '<div class="alert alert-info">Aucune donnée OF disponible.</div>';
  }

  const data = nestedData.main_of;

  if (data.length === 0) {
    return '<div class="alert alert-info">Aucune donnée OF disponible.</div>';
  }

  const tableRows = data
    .slice(0, 100)
    .map(
      (row) => `
        <tr>
            <td>${row.NUMERO_OFDA || ""}</td>
            <td>${row.PRODUIT || ""}</td>
            <td>${row.DESIGNATION || ""}</td>
            <td><span class="badge bg-${getStatusBadgeClass(row.STATUT)}">${
        row.STATUT || ""
      }</span></td>
            <td>${row.CLIENT || ""}</td>
            <td>${row.FAMILLE_TECHNIQUE || ""}</td>
            <td>${row.LANCEMENT_AU_PLUS_TARD || ""}</td>
            <td>${row.QUANTITE_DEMANDEE || ""}</td>
            <td>${row.CUMUL_ENTREES || ""}</td>
            <td>${formatPercentage(row.Avancement_PROD)}</td>
            <td>${row.DUREE_PREVUE || ""}</td>
            <td>${row.CUMUL_TEMPS_PASSES || ""}</td>
            <td>${formatPercentage(row.Avancement_temps)}</td>
            <td>${
              row.EFFICACITE ? parseFloat(row.EFFICACITE).toFixed(2) : ""
            }</td>
            <td>${row.Alerte_temps ? "⚠️ Oui" : "✅ Non"}</td>
        </tr>
    `
    )
    .join("");

  return `
        <div class="mt-3">
            <h4>📋 Données Détaillées des Ordres de Fabrication</h4>
            <p class="text-muted">Affichage des 100 premiers enregistrements</p>
            <div class="table-container">
                <table class="table table-striped table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th>N° OF</th>
                            <th>Produit</th>
                            <th>Désignation</th>
                            <th>Statut</th>
                            <th>Client</th>
                            <th>Famille</th>
                            <th>Lancement</th>
                            <th>Qté Demandée</th>
                            <th>Qté Produite</th>
                            <th>Avanc. Prod.</th>
                            <th>Durée Prévue</th>
                            <th>Temps Passé</th>
                            <th>Avanc. Temps</th>
                            <th>Efficacité</th>
                            <th>Alerte</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${tableRows}
                    </tbody>
                </table>
            </div>
        </div>
    `;
}

function renderChartsTab() {
  return `
        <div class="mt-3">
            <h4>📈 Analyse Graphique de la Production</h4>
            <div class="row mt-4">
                <div class="col-md-6">
                    <div id="scatterChart" style="height: 400px;"></div>
                </div>
                <div class="col-md-6">
                    <div id="efficiencyChart" style="height: 400px;"></div>
                </div>
            </div>
        </div>
    `;
}

function renderReportTab() {
  return `
        <div class="mt-3">
            <h4>📄 Rapport Textuel de Synthèse</h4>
            <div class="d-flex justify-content-end mb-3">
                <button class="btn btn-primary" onclick="generateReport()">
                    <i class="fas fa-file-alt"></i> Générer le Rapport
                </button>
            </div>
            <div id="reportContent">
                <div class="alert alert-info">Cliquez sur "Générer le Rapport" pour créer le rapport de synthèse.</div>
            </div>
        </div>
    `;
}

function renderChargeTab() {
  const nestedData = currentData?.data?.data || currentData?.data;
  if (!nestedData || !nestedData.charge) {
    return '<div class="alert alert-info">Aucune donnée de charge de travail disponible.</div>';
  }

  const data = nestedData.charge;

  if (data.length === 0) {
    return '<div class="alert alert-info">Aucune donnée de charge de travail disponible.</div>';
  }

  const tableRows = data
    .map(
      (row) => `
        <tr>
            <td>${row.SECTEUR || ""}</td>
            <td>${row.NB_OPERATEURS || 0}</td>
            <td>${row.NB_APPRENTIS || 0}</td>
            <td>${row.NB_HEURES_DISPONIBLES_SEMAINE || 0}h</td>
            <td>${row.HEURES_PLANIFIEES || 0}h</td>
            <td>${row.NB_OF_EN_COURS || 0}</td>
            <td><span class="badge bg-${getChargeBadgeClass(
              row.TAUX_CHARGE_POURCENT
            )}">${
        row.TAUX_CHARGE_POURCENT
          ? parseFloat(row.TAUX_CHARGE_POURCENT).toFixed(1)
          : 0
      }%</span></td>
        </tr>
    `
    )
    .join("");

  return `
        <div class="mt-3">
            <h4>⚙️ Analyse de la Charge de Travail</h4>
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead class="table-dark">
                        <tr>
                            <th>Secteur</th>
                            <th>Opérateurs</th>
                            <th>Apprentis</th>
                            <th>Heures Dispo./Sem.</th>
                            <th>Heures Planifiées</th>
                            <th>OF en Cours</th>
                            <th>Taux de Charge</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${tableRows}
                    </tbody>
                </table>
            </div>
        </div>
    `;
}

function renderBacklogTab() {
  const nestedData = currentData?.data?.data || currentData?.data;
  if (!nestedData || !nestedData.backlog) {
    return '<div class="alert alert-info">Aucune donnée de backlog disponible.</div>';
  }

  const data = nestedData.backlog;

  if (data.length === 0) {
    return '<div class="alert alert-info">Aucune donnée de backlog disponible.</div>';
  }

  const tableRows = data
    .map(
      (row) => `
        <tr class="${getPriorityRowClass(row.PRIORITE)}">
            <td>${row.NUMERO_OFDA || ""}</td>
            <td>${row.PRODUIT || ""}</td>
            <td>${row.DESIGNATION || ""}</td>
            <td>${row.CLIENT || ""}</td>
            <td>${row.QUANTITE_RESTANTE || 0}</td>
            <td>${row.LANCEMENT_AU_PLUS_TARD || ""}</td>
            <td>${row.RETARD_JOURS || 0} j</td>
            <td><span class="badge bg-${getPriorityBadgeClass(row.PRIORITE)}">${
        row.PRIORITE || ""
      }</span></td>
            <td>${
              row.TEMPS_RESTANT_ESTIME
                ? parseFloat(row.TEMPS_RESTANT_ESTIME).toFixed(1)
                : 0
            }h</td>
        </tr>
    `
    )
    .join("");

  return `
        <div class="mt-3">
            <h4>⏳ Analyse du Backlog de Production</h4>
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead class="table-dark">
                        <tr>
                            <th>N° OF</th>
                            <th>Produit</th>
                            <th>Désignation</th>
                            <th>Client</th>
                            <th>Qté Restante</th>
                            <th>Lancement Prévu</th>
                            <th>Retard</th>
                            <th>Priorité</th>
                            <th>Temps Restant</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${tableRows}
                    </tbody>
                </table>
            </div>
        </div>
    `;
}

function renderPersonnelTab() {
  const nestedData = currentData?.data?.data || currentData?.data;
  if (!nestedData || !nestedData.personnel) {
    return '<div class="alert alert-info">Aucune donnée de personnel disponible.</div>';
  }

  const data = nestedData.personnel;

  if (data.length === 0) {
    return '<div class="alert alert-info">Aucune donnée de personnel disponible.</div>';
  }

  const tableRows = data
    .map(
      (row) => `
        <tr>
            <td>${row.NOM_COMPLET || ""}</td>
            <td>${row.QUALIFICATION || ""}</td>
            <td>${row.SECTEUR_PERSONNEL || ""}</td>
            <td>${
              row.COEFFICIENT_EFFICACITE
                ? parseFloat(row.COEFFICIENT_EFFICACITE).toFixed(2)
                : ""
            }</td>
            <td><span class="badge bg-success">Actif</span></td>
        </tr>
    `
    )
    .join("");

  return `
        <div class="mt-3">
            <h4>👥 Analyse du Personnel Actif</h4>
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead class="table-dark">
                        <tr>
                            <th>Nom Complet</th>
                            <th>Qualification</th>
                            <th>Secteur</th>
                            <th>Coeff. Efficacité</th>
                            <th>Statut</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${tableRows}
                    </tbody>
                </table>
            </div>
        </div>
    `;
}

// Utility functions
function formatPercentage(value) {
  if (value === null || value === undefined || value === "") return "";
  return (parseFloat(value) * 100).toFixed(1) + "%";
}

function getStatusBadgeClass(status) {
  switch (status) {
    case "C":
      return "warning";
    case "T":
      return "success";
    case "A":
      return "danger";
    default:
      return "secondary";
  }
}

function getChargeBadgeClass(percentage) {
  const pct = parseFloat(percentage) || 0;
  if (pct > 100) return "danger";
  if (pct > 80) return "warning";
  if (pct > 60) return "info";
  return "success";
}

function getPriorityBadgeClass(priority) {
  switch (priority) {
    case "URGENT":
      return "danger";
    case "PRIORITAIRE":
      return "warning";
    case "NORMAL":
      return "success";
    default:
      return "secondary";
  }
}

function getPriorityRowClass(priority) {
  switch (priority) {
    case "URGENT":
      return "table-danger";
    case "PRIORITAIRE":
      return "table-warning";
    default:
      return "";
  }
}

// Chart initialization functions
function initializeCharts() {
  const nestedData = currentData?.data?.data || currentData?.data;
  if (nestedData && nestedData.main_of && nestedData.main_of.length > 0) {
    createStatusPieChart();
    createAlertsBarChart();
    createScatterChart();
    createEfficiencyChart();
  }
}

function createStatusPieChart() {
  const nestedData = currentData?.data?.data || currentData?.data;
  const data = nestedData.main_of;
  const statusCounts = {};

  data.forEach((row) => {
    const status = row.STATUT || "Unknown";
    statusCounts[status] = (statusCounts[status] || 0) + 1;
  });

  const labels = Object.keys(statusCounts);
  const values = Object.values(statusCounts);

  const plotData = [
    {
      values: values,
      labels: labels,
      type: "pie",
      hole: 0.3,
      textinfo: "label+percent",
      textposition: "inside",
    },
  ];

  const layout = {
    title: "Répartition des Statuts OF",
    showlegend: true,
    height: 400,
  };

  Plotly.newPlot("statusPieChart", plotData, layout);
}

function createAlertsBarChart() {
  const nestedData = currentData?.data?.data || currentData?.data;
  const data = nestedData.main_of;
  const alertCounts = { Normal: 0, "En Alerte": 0 };

  data.forEach((row) => {
    if (row.Alerte_temps) {
      alertCounts["En Alerte"]++;
    } else {
      alertCounts["Normal"]++;
    }
  });

  const plotData = [
    {
      x: Object.keys(alertCounts),
      y: Object.values(alertCounts),
      type: "bar",
      marker: {
        color: ["mediumseagreen", "orangered"],
      },
    },
  ];

  const layout = {
    title: "Alertes Temps vs Normaux",
    xaxis: { title: "Statut Alerte" },
    yaxis: { title: "Nombre OF" },
    height: 400,
  };

  Plotly.newPlot("alertsBarChart", plotData, layout);
}

function createScatterChart() {
  const nestedData = currentData?.data?.data || currentData?.data;
  const data = nestedData.main_of;

  const x = data.map((row) => parseFloat(row.Avancement_PROD) || 0);
  const y = data.map((row) => parseFloat(row.Avancement_temps) || 0);
  const text = data.map(
    (row) =>
      `${row.NUMERO_OFDA}<br>${row.PRODUIT}<br>Efficacité: ${
        row.EFFICACITE || "N/A"
      }`
  );
  const colors = data.map((row) => row.STATUT);

  const plotData = [
    {
      x: x,
      y: y,
      mode: "markers",
      type: "scatter",
      text: text,
      hovertemplate: "%{text}<extra></extra>",
      marker: {
        size: 8,
        color: colors,
        colorscale: "Viridis",
      },
    },
  ];

  // Add reference line
  plotData.push({
    x: [0, 1],
    y: [0, 1],
    mode: "lines",
    type: "scatter",
    line: { color: "red", dash: "dash" },
    showlegend: false,
    hoverinfo: "skip",
  });

  const layout = {
    title: "Corrélation Avancement Production et Temps",
    xaxis: {
      title: "Avancement Production (%)",
      tickformat: ".0%",
    },
    yaxis: {
      title: "Avancement Temps (%)",
      tickformat: ".0%",
    },
    height: 400,
  };

  Plotly.newPlot("scatterChart", plotData, layout);
}

// Export functionality
async function exportData(format) {
  try {
    showInfo(`Préparation de l'export ${format.toUpperCase()}...`);

    const dateDebut = document.getElementById("dateDebut").value;
    const dateFin = document.getElementById("dateFin").value;
    const statutFilter = document.getElementById("statutFilter").value;

    const params = new URLSearchParams();
    if (dateDebut) params.append("date_debut", dateDebut);
    if (dateFin) params.append("date_fin", dateFin);
    if (statutFilter) params.append("statut_filter", statutFilter);

    let endpoint;
    if (format === "csv") {
      endpoint = `${API_ENDPOINTS.export.csv}/dashboard?${params}`;
    } else if (format === "excel") {
      endpoint = `${API_ENDPOINTS.export.excel}/dashboard?${params}`;
    }

    const response = await fetch(endpoint);
    if (response.ok) {
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `dashboard_export_${
        new Date().toISOString().split("T")[0]
      }.${format}`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      showInfo(`Export ${format.toUpperCase()} téléchargé avec succès`);
    } else {
      throw new Error(`Export failed: ${response.status}`);
    }
  } catch (error) {
    console.error("Export error:", error);
    showError(`Erreur lors de l'export: ${error.message}`);
  }
}

// Quick view functionality
async function loadQuickView(type) {
  try {
    showInfo(`Chargement de la vue ${type}...`);

    let endpoint;
    switch (type) {
      case "en_cours":
        endpoint = API_ENDPOINTS.of.en_cours;
        break;
      case "urgent":
        endpoint = API_ENDPOINTS.backlog.urgent;
        break;
      default:
        throw new Error(`Unknown view type: ${type}`);
    }

    const response = await fetch(endpoint);
    if (response.ok) {
      const data = await response.json();
      if (data.success) {
        displayQuickViewModal(type, data.data);
      } else {
        throw new Error(data.message || "Failed to load quick view");
      }
    } else {
      throw new Error(`HTTP error: ${response.status}`);
    }
  } catch (error) {
    console.error("Quick view error:", error);
    showError(`Erreur lors du chargement: ${error.message}`);
  }
}

// Health check functionality
async function checkSystemHealth() {
  try {
    showInfo("Vérification de l'état du système...");

    const response = await fetch(API_ENDPOINTS.health.database);
    if (response.ok) {
      const data = await response.json();
      if (data.success) {
        showInfo("✅ Système opérationnel - Base de données accessible");
      } else {
        showError("⚠️ Problème détecté avec la base de données");
      }
    } else {
      throw new Error(`Health check failed: ${response.status}`);
    }
  } catch (error) {
    console.error("Health check error:", error);
    showError(`Erreur lors de la vérification: ${error.message}`);
  }
}

// Modal display for quick views
function displayQuickViewModal(type, data) {
  const modalHtml = `
    <div class="modal fade" id="quickViewModal" tabindex="-1">
      <div class="modal-dialog modal-xl">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">Vue Rapide: ${
              type === "en_cours" ? "OF En Cours" : "Backlog Urgent"
            }</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
          </div>
          <div class="modal-body">
            <div class="table-responsive">
              <table class="table table-striped">
                <thead class="table-dark">
                  ${generateQuickViewTableHeader(type)}
                </thead>
                <tbody>
                  ${generateQuickViewTableRows(type, data)}
                </tbody>
              </table>
            </div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
          </div>
        </div>
      </div>
    </div>
  `;

  // Remove existing modal if any
  const existingModal = document.getElementById("quickViewModal");
  if (existingModal) {
    existingModal.remove();
  }

  // Add new modal
  document.body.insertAdjacentHTML("beforeend", modalHtml);

  // Show modal
  const modal = new bootstrap.Modal(document.getElementById("quickViewModal"));
  modal.show();
}

function generateQuickViewTableHeader(type) {
  if (type === "en_cours") {
    return `
      <tr>
        <th>N° OF</th>
        <th>Produit</th>
        <th>Statut</th>
        <th>Lancement</th>
        <th>Qté Demandée</th>
        <th>Qté Produite</th>
      </tr>
    `;
  } else if (type === "urgent") {
    return `
      <tr>
        <th>N° OF</th>
        <th>Produit</th>
        <th>Client</th>
        <th>Priorité</th>
        <th>Retard (jours)</th>
        <th>Qté Restante</th>
      </tr>
    `;
  }
  return "";
}

function generateQuickViewTableRows(type, data) {
  if (!data || !Array.isArray(data))
    return '<tr><td colspan="6">Aucune donnée disponible</td></tr>';

  return data
    .slice(0, 50)
    .map((row) => {
      if (type === "en_cours") {
        return `
        <tr>
          <td>${row.NUMERO_OFDA || ""}</td>
          <td>${row.PRODUIT || ""}</td>
          <td><span class="badge bg-warning">${row.STATUT || ""}</span></td>
          <td>${row.LANCEMENT_AU_PLUS_TARD || ""}</td>
          <td>${row.QUANTITE_DEMANDEE || ""}</td>
          <td>${row.CUMUL_ENTREES || ""}</td>
        </tr>
      `;
      } else if (type === "urgent") {
        return `
        <tr>
          <td>${row.NUMERO_OFDA || ""}</td>
          <td>${row.PRODUIT || ""}</td>
          <td>${row.CLIENT || ""}</td>
          <td><span class="badge bg-danger">${row.PRIORITE || ""}</span></td>
          <td>${row.RETARD_JOURS || 0}</td>
          <td>${row.QUANTITE_RESTANTE || 0}</td>
        </tr>
      `;
      }
      return "";
    })
    .join("");
}

function createEfficiencyChart() {
  const nestedData = currentData?.data?.data || currentData?.data;
  const data = nestedData.main_of;
  const efficiencyValues = data
    .map((row) => parseFloat(row.EFFICACITE))
    .filter((val) => val > 0 && val < 5); // Filter out extreme values

  const plotData = [
    {
      x: efficiencyValues,
      type: "histogram",
      nbinsx: 30,
      marker: { color: "skyblue" },
    },
  ];

  const layout = {
    title: "Distribution de l'Efficacité des OF",
    xaxis: { title: "Efficacité (Prévu/Passé)" },
    yaxis: { title: "Nombre OF" },
    height: 400,
  };

  Plotly.newPlot("efficiencyChart", plotData, layout);
}

// Report generation
async function generateReport() {
  try {
    showInfo("Génération du rapport en cours...");

    if (!currentData || !currentData.data) {
      throw new Error("Aucune donnée disponible pour générer le rapport");
    }

    // Generate report from current data
    const report = generateReportFromData(currentData);

    document.getElementById("reportContent").innerHTML = `
      <div class="card">
        <div class="card-header">
          <h5><i class="fas fa-file-alt"></i> Rapport de Production</h5>
        </div>
        <div class="card-body">
          <div style="white-space: pre-wrap; font-family: 'Courier New', monospace; font-size: 0.9rem; line-height: 1.4;">
            ${report}
          </div>
        </div>
      </div>
      <div class="mt-3 d-flex gap-2">
        <button class="btn btn-success" onclick="downloadReport(\`${report.replace(
          /`/g,
          "\\`"
        )}\`)">
          <i class="fas fa-download"></i> Télécharger TXT
        </button>
        <button class="btn btn-info" onclick="exportData('csv')">
          <i class="fas fa-file-csv"></i> Exporter CSV
        </button>
        <button class="btn btn-warning" onclick="printReport()">
          <i class="fas fa-print"></i> Imprimer
        </button>
      </div>
    `;

    showInfo("Rapport généré avec succès");
  } catch (error) {
    console.error("Error generating report:", error);
    document.getElementById("reportContent").innerHTML = `
      <div class="alert alert-danger">
        <i class="fas fa-exclamation-triangle"></i>
        Erreur lors de la génération du rapport: ${error.message}
      </div>
    `;
    showError(`Erreur rapport: ${error.message}`);
  }
}

function generateReportFromData(data) {
  const now = new Date();
  const kpis = data.kpis || data.data?.kpis || {};
  const nestedData = data.data?.data || data.data || {};
  const mainOf = nestedData.main_of || [];
  const charge = nestedData.charge || [];
  const backlog = nestedData.backlog || [];

  return `
═══════════════════════════════════════════════════════════════
                    RAPPORT DE PRODUCTION
                      Excalibur ERP
═══════════════════════════════════════════════════════════════

📅 Date de génération: ${now.toLocaleDateString(
    "fr-FR"
  )} à ${now.toLocaleTimeString("fr-FR")}
📊 Période analysée: ${data.filters?.date_debut || "N/A"} → ${
    data.filters?.date_fin || "N/A"
  }
🔍 Filtre statut: ${data.filters?.statut_filter || "Tous"}

═══════════════════════════════════════════════════════════════
                        INDICATEURS CLÉS
═══════════════════════════════════════════════════════════════

📋 Total OF analysés:           ${kpis.total_of || 0}
▶️  OF en cours:                ${kpis.of_en_cours || 0}
✅ OF terminés:                 ${kpis.of_termines || 0}
⏹️  OF arrêtés:                 ${kpis.of_arretes || 0}

📈 Avancement production moyen: ${(kpis.avg_prod || 0).toFixed(1)}%
⏱️  Avancement temps moyen:     ${(kpis.avg_temps || 0).toFixed(1)}%
⚠️  OF en alerte temps:         ${kpis.alertes || 0}
🎯 Efficacité moyenne:          ${(kpis.efficacite || 0).toFixed(2)}

═══════════════════════════════════════════════════════════════
                      ANALYSE DÉTAILLÉE
═══════════════════════════════════════════════════════════════

🏭 CHARGE DE TRAVAIL:
${
  charge.length > 0
    ? charge
        .map(
          (row) =>
            `   • ${row.SECTEUR || "N/A"}: ${
              row.NB_OPERATEURS || 0
            } opérateurs, charge ${(row.TAUX_CHARGE_POURCENT || 0).toFixed(1)}%`
        )
        .join("\n")
    : "   Aucune donnée de charge disponible"
}

📋 BACKLOG CRITIQUE:
${
  backlog.filter((row) => row.PRIORITE === "URGENT").length > 0
    ? backlog
        .filter((row) => row.PRIORITE === "URGENT")
        .slice(0, 5)
        .map(
          (row) =>
            `   • ${row.NUMERO_OFDA || "N/A"} - ${row.PRODUIT || "N/A"} (${
              row.RETARD_JOURS || 0
            }j de retard)`
        )
        .join("\n")
    : "   Aucun élément urgent dans le backlog"
}

🔍 OF EN ALERTE:
${
  mainOf.filter((row) => row.Alerte_temps).length > 0
    ? mainOf
        .filter((row) => row.Alerte_temps)
        .slice(0, 5)
        .map(
          (row) =>
            `   • ${row.NUMERO_OFDA || "N/A"} - ${row.PRODUIT || "N/A"} (${(
              row.Avancement_temps * 100
            ).toFixed(1)}% temps)`
        )
        .join("\n")
    : "   Aucun OF en alerte temps"
}

═══════════════════════════════════════════════════════════════
                      RECOMMANDATIONS
═══════════════════════════════════════════════════════════════

${generateRecommendations(kpis, mainOf, charge, backlog)}

═══════════════════════════════════════════════════════════════
Rapport généré automatiquement par Excalibur ERP
Total enregistrements analysés: ${
    mainOf.length + charge.length + backlog.length
  }
═══════════════════════════════════════════════════════════════
  `.trim();
}

function generateRecommendations(kpis, mainOf, charge, backlog) {
  const recommendations = [];

  if (kpis.alertes > 0) {
    recommendations.push(
      `⚠️  ${kpis.alertes} OF en alerte temps nécessitent une attention immédiate`
    );
  }

  if (kpis.efficacite < 1.0) {
    recommendations.push(
      `📉 Efficacité moyenne faible (${kpis.efficacite.toFixed(
        2
      )}) - Analyser les causes de dépassement`
    );
  }

  const overloadedSectors = charge.filter(
    (row) => (row.TAUX_CHARGE_POURCENT || 0) > 100
  );
  if (overloadedSectors.length > 0) {
    recommendations.push(
      `🔴 ${overloadedSectors.length} secteur(s) en surcharge - Redistribuer la charge`
    );
  }

  const urgentBacklog = backlog.filter(
    (row) => row.PRIORITE === "URGENT"
  ).length;
  if (urgentBacklog > 0) {
    recommendations.push(
      `🚨 ${urgentBacklog} élément(s) urgent(s) en backlog - Prioriser le traitement`
    );
  }

  if (kpis.avg_prod < 50) {
    recommendations.push(
      `📊 Avancement production faible (${kpis.avg_prod.toFixed(
        1
      )}%) - Vérifier les approvisionnements`
    );
  }

  if (recommendations.length === 0) {
    recommendations.push(
      `✅ Situation globalement satisfaisante - Maintenir le niveau de performance`
    );
  }

  return recommendations.map((rec) => `• ${rec}`).join("\n");
}

function printReport() {
  const reportContent = document.querySelector(
    "#reportContent .card-body"
  ).innerHTML;
  const printWindow = window.open("", "_blank");
  printWindow.document.write(`
    <html>
      <head>
        <title>Rapport de Production</title>
        <style>
          body { font-family: 'Courier New', monospace; margin: 20px; }
          pre { white-space: pre-wrap; }
        </style>
      </head>
      <body>
        ${reportContent}
      </body>
    </html>
  `);
  printWindow.document.close();
  printWindow.print();
}

function downloadReport(reportText) {
  const blob = new Blob([reportText], { type: "text/plain" });
  const url = window.URL.createObjectURL(blob);
  const a = document.createElement("a");
  a.href = url;
  a.download = `rapport_production_${
    new Date().toISOString().split("T")[0]
  }.txt`;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  window.URL.revokeObjectURL(url);
}
