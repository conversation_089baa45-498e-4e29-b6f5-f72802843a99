"""
Charge de travail (Workload) routes for production capacity analysis.
"""

from fastapi import APIRouter, HTTPException, Depends, Query
from typing import Optional, List, Dict, Any
from datetime import datetime, timedelta
import pandas as pd

from app.core.database import get_analyzer, get_db_connection
from app.models.common import SuccessResponse

router = APIRouter(prefix="/api/charge", tags=["Charge de Travail"])


def format_dataframe_for_json(df: pd.DataFrame) -> List[Dict[str, Any]]:
    """Convert DataFrame to JSON-serializable format."""
    if df is None or df.empty:
        return []
    
    # Convert datetime columns to strings
    for col in df.select_dtypes(include=['datetime64[ns]', 'datetimetz']).columns:
        df[col] = df[col].dt.strftime('%Y-%m-%d %H:%M:%S')
    
    # Convert to dict and handle NaN values
    return df.fillna('').to_dict('records')


@router.get("/", response_model=SuccessResponse)
async def get_charge_data(
    date_debut: Optional[str] = Query(None, description="Start date (YYYY-MM-DD)"),
    date_fin: Optional[str] = Query(None, description="End date (YYYY-MM-DD)"),
    analyzer=Depends(get_analyzer)
):
    """Get workload analysis data."""
    try:
        # Set default dates if not provided
        if not date_debut:
            date_debut = (datetime.now() - timedelta(days=90)).strftime('%Y-%m-%d')
        if not date_fin:
            date_fin = datetime.now().strftime('%Y-%m-%d')
        
        # Get charge data
        df = analyzer.get_charge_travail_data(date_debut, date_fin)
        
        return SuccessResponse(data={
            "charge_data": format_dataframe_for_json(df),
            "count": len(df) if df is not None else 0,
            "period": {
                "date_debut": date_debut,
                "date_fin": date_fin
            }
        })
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching charge data: {str(e)}")


@router.get("/by_sector", response_model=SuccessResponse)
async def get_charge_by_sector(
    date_debut: Optional[str] = Query(None, description="Start date (YYYY-MM-DD)"),
    date_fin: Optional[str] = Query(None, description="End date (YYYY-MM-DD)"),
    analyzer=Depends(get_analyzer)
):
    """Get workload data grouped by sector."""
    try:
        # Set default dates if not provided
        if not date_debut:
            date_debut = (datetime.now() - timedelta(days=90)).strftime('%Y-%m-%d')
        if not date_fin:
            date_fin = datetime.now().strftime('%Y-%m-%d')
        
        # Get charge data
        df = analyzer.get_charge_travail_data(date_debut, date_fin)
        
        if df is not None and not df.empty:
            # Group by sector and calculate totals
            sector_summary = df.groupby('SECTEUR').agg({
                'NB_OPERATEURS': 'sum',
                'NB_APPRENTIS': 'sum',
                'NB_HEURES_DISPONIBLES_SEMAINE': 'sum',
                'HEURES_PLANIFIEES': 'sum',
                'NB_OF_EN_COURS': 'sum',
                'TAUX_CHARGE_POURCENT': 'mean'
            }).reset_index()
            
            sector_data = sector_summary.to_dict('records')
        else:
            sector_data = []
        
        return SuccessResponse(data={
            "sector_data": sector_data,
            "count": len(sector_data),
            "period": {
                "date_debut": date_debut,
                "date_fin": date_fin
            }
        })
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching sector charge data: {str(e)}")


@router.get("/capacity", response_model=SuccessResponse)
async def get_capacity_analysis():
    """Get capacity analysis based on personnel data."""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # Get personnel capacity by sector
        query = """
        SELECT
            CASE
                WHEN UPPER(COALESCE(S.QUALIFICATION, '')) LIKE '%CMS%' THEN 'CMS'
                WHEN UPPER(COALESCE(S.QUALIFICATION, '')) LIKE '%ASSEMBLAGE%' THEN 'ASSEMBLAGE'
                WHEN UPPER(COALESCE(S.QUALIFICATION, '')) LIKE '%CABLE%' THEN 'CABLAGE'
                WHEN UPPER(COALESCE(S.QUALIFICATION, '')) LIKE '%TEST%' THEN 'TEST'
                ELSE 'GENERAL'
            END AS SECTEUR,
            COUNT(*) AS TOTAL_PERSONNEL,
            COUNT(CASE WHEN UPPER(COALESCE(S.QUALIFICATION, '')) NOT LIKE '%APPRENTI%' THEN 1 END) AS NB_OPERATEURS,
            COUNT(CASE WHEN UPPER(COALESCE(S.QUALIFICATION, '')) LIKE '%APPRENTI%' THEN 1 END) AS NB_APPRENTIS,
            COUNT(CASE WHEN UPPER(COALESCE(S.QUALIFICATION, '')) LIKE '%EXPERT%' THEN 1 END) AS NB_EXPERTS,
            COUNT(CASE WHEN UPPER(COALESCE(S.QUALIFICATION, '')) LIKE '%SENIOR%' THEN 1 END) AS NB_SENIORS,
            (COUNT(*) * 40) AS HEURES_DISPONIBLES_SEMAINE,
            (COUNT(*) * 8) AS HEURES_DISPONIBLES_JOUR
        FROM gpao.SALARIES S
        WHERE S.ACTIF = 1
        GROUP BY SECTEUR
        ORDER BY SECTEUR
        """
        
        cursor.execute(query)
        rows = cursor.fetchall()
        
        capacity_data = []
        for row in rows:
            capacity_data.append({
                "SECTEUR": row[0],
                "TOTAL_PERSONNEL": row[1],
                "NB_OPERATEURS": row[2],
                "NB_APPRENTIS": row[3],
                "NB_EXPERTS": row[4],
                "NB_SENIORS": row[5],
                "HEURES_DISPONIBLES_SEMAINE": row[6],
                "HEURES_DISPONIBLES_JOUR": row[7]
            })
        
        cursor.close()
        conn.close()
        
        return SuccessResponse(data={
            "capacity_data": capacity_data,
            "count": len(capacity_data)
        })
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching capacity analysis: {str(e)}")


@router.get("/overload", response_model=SuccessResponse)
async def get_overload_analysis(
    date_debut: Optional[str] = Query(None, description="Start date (YYYY-MM-DD)"),
    date_fin: Optional[str] = Query(None, description="End date (YYYY-MM-DD)"),
    threshold: float = Query(100.0, description="Overload threshold percentage"),
    analyzer=Depends(get_analyzer)
):
    """Get sectors with workload above threshold."""
    try:
        # Set default dates if not provided
        if not date_debut:
            date_debut = (datetime.now() - timedelta(days=90)).strftime('%Y-%m-%d')
        if not date_fin:
            date_fin = datetime.now().strftime('%Y-%m-%d')
        
        # Get charge data
        df = analyzer.get_charge_travail_data(date_debut, date_fin)
        
        if df is not None and not df.empty:
            # Filter sectors above threshold
            overloaded = df[df['TAUX_CHARGE_POURCENT'] > threshold]
            overload_data = format_dataframe_for_json(overloaded)
        else:
            overload_data = []
        
        return SuccessResponse(data={
            "overload_data": overload_data,
            "count": len(overload_data),
            "threshold": threshold,
            "period": {
                "date_debut": date_debut,
                "date_fin": date_fin
            }
        })
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching overload analysis: {str(e)}")


@router.get("/efficiency", response_model=SuccessResponse)
async def get_efficiency_analysis():
    """Get efficiency analysis by personnel qualification."""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # Get efficiency coefficients by qualification
        query = """
        SELECT
            COALESCE(S.QUALIFICATION, 'N/A') AS QUALIFICATION,
            COUNT(*) AS NB_PERSONNEL,
            CASE
                WHEN UPPER(COALESCE(S.QUALIFICATION, '')) LIKE '%EXPERT%' THEN 1.2
                WHEN UPPER(COALESCE(S.QUALIFICATION, '')) LIKE '%SENIOR%' THEN 1.1
                WHEN UPPER(COALESCE(S.QUALIFICATION, '')) LIKE '%JUNIOR%' THEN 0.9
                WHEN UPPER(COALESCE(S.QUALIFICATION, '')) LIKE '%APPRENTI%' THEN 0.7
                ELSE 1.0
            END AS COEFFICIENT_EFFICACITE,
            CASE
                WHEN UPPER(COALESCE(S.QUALIFICATION, '')) LIKE '%CMS%' THEN 'CMS'
                WHEN UPPER(COALESCE(S.QUALIFICATION, '')) LIKE '%ASSEMBLAGE%' THEN 'ASSEMBLAGE'
                WHEN UPPER(COALESCE(S.QUALIFICATION, '')) LIKE '%CABLE%' THEN 'CABLAGE'
                WHEN UPPER(COALESCE(S.QUALIFICATION, '')) LIKE '%TEST%' THEN 'TEST'
                ELSE 'GENERAL'
            END AS SECTEUR
        FROM gpao.SALARIES S
        WHERE S.ACTIF = 1
        GROUP BY QUALIFICATION, COEFFICIENT_EFFICACITE, SECTEUR
        ORDER BY SECTEUR, COEFFICIENT_EFFICACITE DESC
        """
        
        cursor.execute(query)
        rows = cursor.fetchall()
        
        efficiency_data = []
        for row in rows:
            efficiency_data.append({
                "QUALIFICATION": row[0],
                "NB_PERSONNEL": row[1],
                "COEFFICIENT_EFFICACITE": row[2],
                "SECTEUR": row[3],
                "HEURES_EFFECTIVES_SEMAINE": row[1] * 40 * row[2]  # Personnel * 40h * coefficient
            })
        
        cursor.close()
        conn.close()
        
        return SuccessResponse(data={
            "efficiency_data": efficiency_data,
            "count": len(efficiency_data)
        })
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching efficiency analysis: {str(e)}")


@router.get("/planning", response_model=SuccessResponse)
async def get_planning_data(
    date_debut: Optional[str] = Query(None, description="Start date (YYYY-MM-DD)"),
    date_fin: Optional[str] = Query(None, description="End date (YYYY-MM-DD)"),
    analyzer=Depends(get_analyzer)
):
    """Get planning data for workload distribution."""
    try:
        # Set default dates if not provided
        if not date_debut:
            date_debut = (datetime.now() - timedelta(days=90)).strftime('%Y-%m-%d')
        if not date_fin:
            date_fin = datetime.now().strftime('%Y-%m-%d')
        
        # Get charge data and OF data
        charge_df = analyzer.get_charge_travail_data(date_debut, date_fin)
        of_df = analyzer.get_comprehensive_of_data(date_debut, date_fin, 'C')  # Only en cours
        
        planning_data = {
            "charge_summary": format_dataframe_for_json(charge_df),
            "of_en_cours": format_dataframe_for_json(of_df),
            "period": {
                "date_debut": date_debut,
                "date_fin": date_fin
            }
        }
        
        return SuccessResponse(data=planning_data)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching planning data: {str(e)}")
