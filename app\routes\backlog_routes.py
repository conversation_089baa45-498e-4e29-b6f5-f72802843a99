"""
Backlog routes for production order backlog management.
"""

from fastapi import APIRouter, HTTPException, Depends, Query
from typing import Optional, List, Dict, Any
from datetime import datetime, timedelta
import pandas as pd

from app.core.database import get_analyzer, get_db_connection
from app.models.common import SuccessResponse

router = APIRouter(prefix="/api/backlog", tags=["Backlog"])


def format_dataframe_for_json(df: pd.DataFrame) -> List[Dict[str, Any]]:
    """Convert DataFrame to JSON-serializable format."""
    if df is None or df.empty:
        return []
    
    # Convert datetime columns to strings
    for col in df.select_dtypes(include=['datetime64[ns]', 'datetimetz']).columns:
        df[col] = df[col].dt.strftime('%Y-%m-%d %H:%M:%S')
    
    # Convert to dict and handle NaN values
    return df.fillna('').to_dict('records')


@router.get("/", response_model=SuccessResponse)
async def get_backlog_data(
    date_debut: Optional[str] = Query(None, description="Start date (YYYY-MM-DD)"),
    date_fin: Optional[str] = Query(None, description="End date (YYYY-MM-DD)"),
    analyzer=Depends(get_analyzer)
):
    """Get backlog data."""
    try:
        # Set default dates if not provided
        if not date_debut:
            date_debut = (datetime.now() - timedelta(days=90)).strftime('%Y-%m-%d')
        if not date_fin:
            date_fin = datetime.now().strftime('%Y-%m-%d')
        
        # Get backlog data
        df = analyzer.get_backlog_data(date_debut, date_fin)
        
        return SuccessResponse(data={
            "backlog_data": format_dataframe_for_json(df),
            "count": len(df) if df is not None else 0,
            "period": {
                "date_debut": date_debut,
                "date_fin": date_fin
            }
        })
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching backlog data: {str(e)}")


@router.get("/urgent", response_model=SuccessResponse)
async def get_urgent_backlog(
    date_debut: Optional[str] = Query(None, description="Start date (YYYY-MM-DD)"),
    date_fin: Optional[str] = Query(None, description="End date (YYYY-MM-DD)"),
    analyzer=Depends(get_analyzer)
):
    """Get urgent backlog items (overdue orders)."""
    try:
        # Set default dates if not provided
        if not date_debut:
            date_debut = (datetime.now() - timedelta(days=90)).strftime('%Y-%m-%d')
        if not date_fin:
            date_fin = datetime.now().strftime('%Y-%m-%d')
        
        # Get backlog data
        df = analyzer.get_backlog_data(date_debut, date_fin)
        
        if df is not None and not df.empty:
            # Filter urgent items
            urgent_df = df[df['PRIORITE'] == 'URGENT']
            urgent_data = format_dataframe_for_json(urgent_df)
        else:
            urgent_data = []
        
        return SuccessResponse(data={
            "urgent_backlog": urgent_data,
            "count": len(urgent_data),
            "period": {
                "date_debut": date_debut,
                "date_fin": date_fin
            }
        })
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching urgent backlog: {str(e)}")


@router.get("/by_priority/{priority}", response_model=SuccessResponse)
async def get_backlog_by_priority(
    priority: str,
    date_debut: Optional[str] = Query(None, description="Start date (YYYY-MM-DD)"),
    date_fin: Optional[str] = Query(None, description="End date (YYYY-MM-DD)"),
    analyzer=Depends(get_analyzer)
):
    """Get backlog items by priority (URGENT, PRIORITAIRE, NORMAL)."""
    try:
        # Set default dates if not provided
        if not date_debut:
            date_debut = (datetime.now() - timedelta(days=90)).strftime('%Y-%m-%d')
        if not date_fin:
            date_fin = datetime.now().strftime('%Y-%m-%d')
        
        # Validate priority
        valid_priorities = ['URGENT', 'PRIORITAIRE', 'NORMAL']
        if priority.upper() not in valid_priorities:
            raise HTTPException(
                status_code=400, 
                detail=f"Invalid priority. Must be one of: {', '.join(valid_priorities)}"
            )
        
        # Get backlog data
        df = analyzer.get_backlog_data(date_debut, date_fin)
        
        if df is not None and not df.empty:
            # Filter by priority
            priority_df = df[df['PRIORITE'] == priority.upper()]
            priority_data = format_dataframe_for_json(priority_df)
        else:
            priority_data = []
        
        return SuccessResponse(data={
            "backlog_data": priority_data,
            "count": len(priority_data),
            "priority": priority.upper(),
            "period": {
                "date_debut": date_debut,
                "date_fin": date_fin
            }
        })
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching backlog by priority: {str(e)}")


@router.get("/by_client/{client}", response_model=SuccessResponse)
async def get_backlog_by_client(
    client: str,
    date_debut: Optional[str] = Query(None, description="Start date (YYYY-MM-DD)"),
    date_fin: Optional[str] = Query(None, description="End date (YYYY-MM-DD)"),
    analyzer=Depends(get_analyzer)
):
    """Get backlog items by client."""
    try:
        # Set default dates if not provided
        if not date_debut:
            date_debut = (datetime.now() - timedelta(days=90)).strftime('%Y-%m-%d')
        if not date_fin:
            date_fin = datetime.now().strftime('%Y-%m-%d')
        
        # Get backlog data
        df = analyzer.get_backlog_data(date_debut, date_fin)
        
        if df is not None and not df.empty:
            # Filter by client
            client_df = df[df['CLIENT'].str.contains(client, case=False, na=False)]
            client_data = format_dataframe_for_json(client_df)
        else:
            client_data = []
        
        return SuccessResponse(data={
            "backlog_data": client_data,
            "count": len(client_data),
            "client": client,
            "period": {
                "date_debut": date_debut,
                "date_fin": date_fin
            }
        })
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching backlog by client: {str(e)}")


@router.get("/summary", response_model=SuccessResponse)
async def get_backlog_summary(
    date_debut: Optional[str] = Query(None, description="Start date (YYYY-MM-DD)"),
    date_fin: Optional[str] = Query(None, description="End date (YYYY-MM-DD)"),
    analyzer=Depends(get_analyzer)
):
    """Get backlog summary statistics."""
    try:
        # Set default dates if not provided
        if not date_debut:
            date_debut = (datetime.now() - timedelta(days=90)).strftime('%Y-%m-%d')
        if not date_fin:
            date_fin = datetime.now().strftime('%Y-%m-%d')
        
        # Get backlog data
        df = analyzer.get_backlog_data(date_debut, date_fin)
        
        if df is not None and not df.empty:
            # Calculate summary statistics
            summary = {
                "total_items": len(df),
                "urgent_count": len(df[df['PRIORITE'] == 'URGENT']),
                "prioritaire_count": len(df[df['PRIORITE'] == 'PRIORITAIRE']),
                "normal_count": len(df[df['PRIORITE'] == 'NORMAL']),
                "total_quantity_remaining": df['QUANTITE_RESTANTE'].sum(),
                "total_time_remaining": df['TEMPS_RESTANT_ESTIME'].sum(),
                "avg_delay_days": df[df['RETARD_JOURS'] > 0]['RETARD_JOURS'].mean() if len(df[df['RETARD_JOURS'] > 0]) > 0 else 0,
                "max_delay_days": df['RETARD_JOURS'].max(),
                "clients_count": df['CLIENT'].nunique(),
                "products_count": df['PRODUIT'].nunique()
            }
            
            # Priority distribution
            priority_dist = df['PRIORITE'].value_counts().to_dict()
            
            # Client distribution (top 10)
            client_dist = df['CLIENT'].value_counts().head(10).to_dict()
            
        else:
            summary = {
                "total_items": 0,
                "urgent_count": 0,
                "prioritaire_count": 0,
                "normal_count": 0,
                "total_quantity_remaining": 0,
                "total_time_remaining": 0,
                "avg_delay_days": 0,
                "max_delay_days": 0,
                "clients_count": 0,
                "products_count": 0
            }
            priority_dist = {}
            client_dist = {}
        
        return SuccessResponse(data={
            "summary": summary,
            "priority_distribution": priority_dist,
            "client_distribution": client_dist,
            "period": {
                "date_debut": date_debut,
                "date_fin": date_fin
            }
        })
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching backlog summary: {str(e)}")


@router.get("/overdue", response_model=SuccessResponse)
async def get_overdue_orders():
    """Get overdue production orders."""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # Query for overdue orders
        query = """
        SELECT
            OF_DA.PRODUIT,
            OF_DA.DESIGNATION,
            OF_DA.NUMERO_OFDA,
            OF_DA.AFFAIRE AS NUMERO_COMMANDE,
            OF_DA.QUANTITE_DEMANDEE,
            OF_DA.CUMUL_ENTREES,
            (OF_DA.QUANTITE_DEMANDEE - COALESCE(OF_DA.CUMUL_ENTREES, 0)) AS QUANTITE_RESTANTE,
            OF_DA.LANCEMENT_AU_PLUS_TARD,
            OF_DA.STATUT,
            DATEDIFF(day, OF_DA.LANCEMENT_AU_PLUS_TARD, TODAY()) AS RETARD_JOURS,
            CASE
                WHEN OF_DA.DUREE_PREVUE > OF_DA.CUMUL_TEMPS_PASSES
                THEN (OF_DA.DUREE_PREVUE - OF_DA.CUMUL_TEMPS_PASSES)
                ELSE 0.0
            END AS TEMPS_RESTANT_ESTIME,
            COALESCE(OF_DA.CLIENT, 'N/A') AS CLIENT
        FROM gpao.OF_DA OF_DA
        WHERE OF_DA.NUMERO_OFDA LIKE 'F%'
          AND OF_DA.STATUT = 'C'
          AND OF_DA.LANCEMENT_AU_PLUS_TARD < TODAY()
          AND (OF_DA.QUANTITE_DEMANDEE - COALESCE(OF_DA.CUMUL_ENTREES, 0)) > 0
        ORDER BY RETARD_JOURS DESC, OF_DA.LANCEMENT_AU_PLUS_TARD ASC
        """
        
        cursor.execute(query)
        rows = cursor.fetchall()
        
        overdue_data = []
        for row in rows:
            overdue_data.append({
                "PRODUIT": row[0],
                "DESIGNATION": row[1],
                "NUMERO_OFDA": row[2],
                "NUMERO_COMMANDE": row[3],
                "QUANTITE_DEMANDEE": row[4],
                "CUMUL_ENTREES": row[5],
                "QUANTITE_RESTANTE": row[6],
                "LANCEMENT_AU_PLUS_TARD": row[7],
                "STATUT": row[8],
                "RETARD_JOURS": row[9],
                "TEMPS_RESTANT_ESTIME": row[10],
                "CLIENT": row[11],
                "PRIORITE": "URGENT"
            })
        
        cursor.close()
        conn.close()
        
        return SuccessResponse(data={
            "overdue_orders": overdue_data,
            "count": len(overdue_data)
        })
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching overdue orders: {str(e)}")
