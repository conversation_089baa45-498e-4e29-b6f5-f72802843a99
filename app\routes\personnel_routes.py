"""
Personnel routes for employee and workforce management.
"""

from fastapi import APIRouter, HTTPException, Depends, Query
from typing import Optional, List, Dict, Any
import pandas as pd

from app.core.database import get_analyzer, get_db_connection
from app.models.common import SuccessResponse

router = APIRouter(prefix="/api/personnel", tags=["Personnel"])


def format_dataframe_for_json(df: pd.DataFrame) -> List[Dict[str, Any]]:
    """Convert DataFrame to JSON-serializable format."""
    if df is None or df.empty:
        return []
    
    # Convert datetime columns to strings
    for col in df.select_dtypes(include=['datetime64[ns]', 'datetimetz']).columns:
        df[col] = df[col].dt.strftime('%Y-%m-%d %H:%M:%S')
    
    # Convert to dict and handle NaN values
    return df.fillna('').to_dict('records')


@router.get("/", response_model=SuccessResponse)
async def get_personnel_data(analyzer=Depends(get_analyzer)):
    """Get all active personnel data."""
    try:
        # Get personnel data
        df = analyzer.get_personnel_data()
        
        return SuccessResponse(data={
            "personnel_data": format_dataframe_for_json(df),
            "count": len(df) if df is not None else 0
        })
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching personnel data: {str(e)}")


@router.get("/by_sector/{sector}", response_model=SuccessResponse)
async def get_personnel_by_sector(sector: str):
    """Get personnel data by sector."""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # Validate sector
        valid_sectors = ['CMS', 'ASSEMBLAGE', 'CABLAGE', 'TEST', 'GENERAL']
        if sector.upper() not in valid_sectors:
            raise HTTPException(
                status_code=400, 
                detail=f"Invalid sector. Must be one of: {', '.join(valid_sectors)}"
            )
        
        # Query personnel by sector
        query = """
        SELECT
            SALARIES.NOM,
            SALARIES.PRENOM,
            COALESCE(SALARIES.QUALIFICATION, 'N/A') AS QUALIFICATION,
            SALARIES.ACTIF,
            (SALARIES.NOM + ' ' + COALESCE(SALARIES.PRENOM, '')) AS NOM_COMPLET,
            CASE
                WHEN UPPER(COALESCE(SALARIES.QUALIFICATION, '')) LIKE '%EXPERT%' THEN 1.2
                WHEN UPPER(COALESCE(SALARIES.QUALIFICATION, '')) LIKE '%SENIOR%' THEN 1.1
                WHEN UPPER(COALESCE(SALARIES.QUALIFICATION, '')) LIKE '%JUNIOR%' THEN 0.9
                WHEN UPPER(COALESCE(SALARIES.QUALIFICATION, '')) LIKE '%APPRENTI%' THEN 0.7
                ELSE 1.0
            END AS COEFFICIENT_EFFICACITE,
            CASE
                WHEN UPPER(COALESCE(SALARIES.QUALIFICATION, '')) LIKE '%CMS%' THEN 'CMS'
                WHEN UPPER(COALESCE(SALARIES.QUALIFICATION, '')) LIKE '%ASSEMBLAGE%' THEN 'ASSEMBLAGE'
                WHEN UPPER(COALESCE(SALARIES.QUALIFICATION, '')) LIKE '%CABLE%' THEN 'CABLAGE'
                WHEN UPPER(COALESCE(SALARIES.QUALIFICATION, '')) LIKE '%TEST%' THEN 'TEST'
                ELSE 'GENERAL'
            END AS SECTEUR_PERSONNEL
        FROM gpao.SALARIES SALARIES
        WHERE SALARIES.ACTIF = 1
          AND (
            CASE
                WHEN UPPER(COALESCE(SALARIES.QUALIFICATION, '')) LIKE '%CMS%' THEN 'CMS'
                WHEN UPPER(COALESCE(SALARIES.QUALIFICATION, '')) LIKE '%ASSEMBLAGE%' THEN 'ASSEMBLAGE'
                WHEN UPPER(COALESCE(SALARIES.QUALIFICATION, '')) LIKE '%CABLE%' THEN 'CABLAGE'
                WHEN UPPER(COALESCE(SALARIES.QUALIFICATION, '')) LIKE '%TEST%' THEN 'TEST'
                ELSE 'GENERAL'
            END
          ) = ?
        ORDER BY SALARIES.NOM, SALARIES.PRENOM
        """
        
        cursor.execute(query, (sector.upper(),))
        rows = cursor.fetchall()
        
        personnel_data = []
        for row in rows:
            personnel_data.append({
                "NOM": row[0],
                "PRENOM": row[1],
                "QUALIFICATION": row[2],
                "ACTIF": row[3],
                "NOM_COMPLET": row[4],
                "COEFFICIENT_EFFICACITE": row[5],
                "SECTEUR_PERSONNEL": row[6]
            })
        
        cursor.close()
        conn.close()
        
        return SuccessResponse(data={
            "personnel_data": personnel_data,
            "count": len(personnel_data),
            "sector": sector.upper()
        })
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching personnel by sector: {str(e)}")


@router.get("/qualifications", response_model=SuccessResponse)
async def get_qualifications():
    """Get all unique qualifications."""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        query = """
        SELECT DISTINCT
            COALESCE(SALARIES.QUALIFICATION, 'N/A') AS QUALIFICATION,
            COUNT(*) AS NB_PERSONNEL
        FROM gpao.SALARIES SALARIES
        WHERE SALARIES.ACTIF = 1
        GROUP BY QUALIFICATION
        ORDER BY NB_PERSONNEL DESC, QUALIFICATION
        """
        
        cursor.execute(query)
        rows = cursor.fetchall()
        
        qualifications_data = []
        for row in rows:
            qualifications_data.append({
                "QUALIFICATION": row[0],
                "NB_PERSONNEL": row[1]
            })
        
        cursor.close()
        conn.close()
        
        return SuccessResponse(data={
            "qualifications": qualifications_data,
            "count": len(qualifications_data)
        })
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching qualifications: {str(e)}")


@router.get("/efficiency", response_model=SuccessResponse)
async def get_efficiency_coefficients():
    """Get efficiency coefficients by qualification level."""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        query = """
        SELECT
            CASE
                WHEN UPPER(COALESCE(SALARIES.QUALIFICATION, '')) LIKE '%EXPERT%' THEN 'EXPERT'
                WHEN UPPER(COALESCE(SALARIES.QUALIFICATION, '')) LIKE '%SENIOR%' THEN 'SENIOR'
                WHEN UPPER(COALESCE(SALARIES.QUALIFICATION, '')) LIKE '%JUNIOR%' THEN 'JUNIOR'
                WHEN UPPER(COALESCE(SALARIES.QUALIFICATION, '')) LIKE '%APPRENTI%' THEN 'APPRENTI'
                ELSE 'STANDARD'
            END AS NIVEAU,
            CASE
                WHEN UPPER(COALESCE(SALARIES.QUALIFICATION, '')) LIKE '%EXPERT%' THEN 1.2
                WHEN UPPER(COALESCE(SALARIES.QUALIFICATION, '')) LIKE '%SENIOR%' THEN 1.1
                WHEN UPPER(COALESCE(SALARIES.QUALIFICATION, '')) LIKE '%JUNIOR%' THEN 0.9
                WHEN UPPER(COALESCE(SALARIES.QUALIFICATION, '')) LIKE '%APPRENTI%' THEN 0.7
                ELSE 1.0
            END AS COEFFICIENT,
            COUNT(*) AS NB_PERSONNEL,
            CASE
                WHEN UPPER(COALESCE(SALARIES.QUALIFICATION, '')) LIKE '%CMS%' THEN 'CMS'
                WHEN UPPER(COALESCE(SALARIES.QUALIFICATION, '')) LIKE '%ASSEMBLAGE%' THEN 'ASSEMBLAGE'
                WHEN UPPER(COALESCE(SALARIES.QUALIFICATION, '')) LIKE '%CABLE%' THEN 'CABLAGE'
                WHEN UPPER(COALESCE(SALARIES.QUALIFICATION, '')) LIKE '%TEST%' THEN 'TEST'
                ELSE 'GENERAL'
            END AS SECTEUR
        FROM gpao.SALARIES SALARIES
        WHERE SALARIES.ACTIF = 1
        GROUP BY NIVEAU, COEFFICIENT, SECTEUR
        ORDER BY SECTEUR, COEFFICIENT DESC
        """
        
        cursor.execute(query)
        rows = cursor.fetchall()
        
        efficiency_data = []
        for row in rows:
            efficiency_data.append({
                "NIVEAU": row[0],
                "COEFFICIENT": row[1],
                "NB_PERSONNEL": row[2],
                "SECTEUR": row[3],
                "CAPACITE_EFFECTIVE": row[2] * row[1] * 40  # Personnel * coefficient * 40h/semaine
            })
        
        cursor.close()
        conn.close()
        
        return SuccessResponse(data={
            "efficiency_data": efficiency_data,
            "count": len(efficiency_data)
        })
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching efficiency coefficients: {str(e)}")


@router.get("/summary", response_model=SuccessResponse)
async def get_personnel_summary(analyzer=Depends(get_analyzer)):
    """Get personnel summary statistics."""
    try:
        # Get personnel data
        df = analyzer.get_personnel_data()
        
        if df is not None and not df.empty:
            # Calculate summary statistics
            summary = {
                "total_actifs": len(df),
                "qualifications_uniques": df['QUALIFICATION'].nunique(),
                "secteurs": df['SECTEUR_PERSONNEL'].value_counts().to_dict(),
                "niveaux": {
                    "experts": len(df[df['QUALIFICATION'].str.contains('EXPERT', case=False, na=False)]),
                    "seniors": len(df[df['QUALIFICATION'].str.contains('SENIOR', case=False, na=False)]),
                    "juniors": len(df[df['QUALIFICATION'].str.contains('JUNIOR', case=False, na=False)]),
                    "apprentis": len(df[df['QUALIFICATION'].str.contains('APPRENTI', case=False, na=False)]),
                    "standards": len(df[~df['QUALIFICATION'].str.contains('EXPERT|SENIOR|JUNIOR|APPRENTI', case=False, na=False)])
                },
                "coefficient_moyen": df['COEFFICIENT_EFFICACITE'].mean(),
                "capacite_totale_semaine": len(df) * 40,
                "capacite_effective_semaine": (df['COEFFICIENT_EFFICACITE'] * 40).sum()
            }
        else:
            summary = {
                "total_actifs": 0,
                "qualifications_uniques": 0,
                "secteurs": {},
                "niveaux": {
                    "experts": 0,
                    "seniors": 0,
                    "juniors": 0,
                    "apprentis": 0,
                    "standards": 0
                },
                "coefficient_moyen": 0,
                "capacite_totale_semaine": 0,
                "capacite_effective_semaine": 0
            }
        
        return SuccessResponse(data={
            "summary": summary
        })
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching personnel summary: {str(e)}")


@router.get("/search", response_model=SuccessResponse)
async def search_personnel(
    nom: Optional[str] = Query(None, description="Search by last name"),
    prenom: Optional[str] = Query(None, description="Search by first name"),
    qualification: Optional[str] = Query(None, description="Search by qualification"),
    secteur: Optional[str] = Query(None, description="Search by sector")
):
    """Search personnel by various criteria."""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # Build dynamic query
        query = """
        SELECT
            SALARIES.NOM,
            SALARIES.PRENOM,
            COALESCE(SALARIES.QUALIFICATION, 'N/A') AS QUALIFICATION,
            SALARIES.ACTIF,
            (SALARIES.NOM + ' ' + COALESCE(SALARIES.PRENOM, '')) AS NOM_COMPLET,
            CASE
                WHEN UPPER(COALESCE(SALARIES.QUALIFICATION, '')) LIKE '%EXPERT%' THEN 1.2
                WHEN UPPER(COALESCE(SALARIES.QUALIFICATION, '')) LIKE '%SENIOR%' THEN 1.1
                WHEN UPPER(COALESCE(SALARIES.QUALIFICATION, '')) LIKE '%JUNIOR%' THEN 0.9
                WHEN UPPER(COALESCE(SALARIES.QUALIFICATION, '')) LIKE '%APPRENTI%' THEN 0.7
                ELSE 1.0
            END AS COEFFICIENT_EFFICACITE,
            CASE
                WHEN UPPER(COALESCE(SALARIES.QUALIFICATION, '')) LIKE '%CMS%' THEN 'CMS'
                WHEN UPPER(COALESCE(SALARIES.QUALIFICATION, '')) LIKE '%ASSEMBLAGE%' THEN 'ASSEMBLAGE'
                WHEN UPPER(COALESCE(SALARIES.QUALIFICATION, '')) LIKE '%CABLE%' THEN 'CABLAGE'
                WHEN UPPER(COALESCE(SALARIES.QUALIFICATION, '')) LIKE '%TEST%' THEN 'TEST'
                ELSE 'GENERAL'
            END AS SECTEUR_PERSONNEL
        FROM gpao.SALARIES SALARIES
        WHERE SALARIES.ACTIF = 1
        """
        
        conditions = []
        params = []
        
        if nom:
            conditions.append("UPPER(SALARIES.NOM) LIKE ?")
            params.append(f"%{nom.upper()}%")
        
        if prenom:
            conditions.append("UPPER(SALARIES.PRENOM) LIKE ?")
            params.append(f"%{prenom.upper()}%")
        
        if qualification:
            conditions.append("UPPER(COALESCE(SALARIES.QUALIFICATION, '')) LIKE ?")
            params.append(f"%{qualification.upper()}%")
        
        if secteur:
            secteur_condition = """
            (CASE
                WHEN UPPER(COALESCE(SALARIES.QUALIFICATION, '')) LIKE '%CMS%' THEN 'CMS'
                WHEN UPPER(COALESCE(SALARIES.QUALIFICATION, '')) LIKE '%ASSEMBLAGE%' THEN 'ASSEMBLAGE'
                WHEN UPPER(COALESCE(SALARIES.QUALIFICATION, '')) LIKE '%CABLE%' THEN 'CABLAGE'
                WHEN UPPER(COALESCE(SALARIES.QUALIFICATION, '')) LIKE '%TEST%' THEN 'TEST'
                ELSE 'GENERAL'
            END) = ?
            """
            conditions.append(secteur_condition)
            params.append(secteur.upper())
        
        if conditions:
            query += " AND " + " AND ".join(conditions)
        
        query += " ORDER BY SALARIES.NOM, SALARIES.PRENOM"
        
        cursor.execute(query, params)
        rows = cursor.fetchall()
        
        personnel_data = []
        for row in rows:
            personnel_data.append({
                "NOM": row[0],
                "PRENOM": row[1],
                "QUALIFICATION": row[2],
                "ACTIF": row[3],
                "NOM_COMPLET": row[4],
                "COEFFICIENT_EFFICACITE": row[5],
                "SECTEUR_PERSONNEL": row[6]
            })
        
        cursor.close()
        conn.close()
        
        return SuccessResponse(data={
            "personnel_data": personnel_data,
            "count": len(personnel_data),
            "search_criteria": {
                "nom": nom,
                "prenom": prenom,
                "qualification": qualification,
                "secteur": secteur
            }
        })
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error searching personnel: {str(e)}")
